import { useCallback, useState, useMemo, useRef, useEffect } from 'react'
import { UnifiedTrainingData } from '../utils/crew-training-utils'

// Performance optimization: Cache filter results to avoid redundant calculations
const filterCache = new Map<string, UnifiedTrainingData[]>()
const CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues

// Helper function to generate cache key from filter and data
const generateCacheKey = (filter: UnifiedSearchFilter, dataLength: number, dataHash: string): string => {
    return JSON.stringify({ filter, dataLength, dataHash })
}

// Helper function to generate a simple hash from data array
const generateDataHash = (data: UnifiedTrainingData[]): string => {
    if (!data || data.length === 0) return 'empty'
    // Use first and last item IDs plus length for a simple hash
    return `${data[0]?.id}-${data[data.length - 1]?.id}-${data.length}`
}

export interface UnifiedSearchFilter {
    vesselID?: { eq?: number; in?: number[] }
    trainingTypes?: { id: { contains?: number; in?: number[] } }
    trainer?: { id: { eq?: number; in?: number[] } }
    members?: { id: { eq?: number; in?: number[] } }
    date?: { gte: Date; lte: Date }
    category?: 'all' | 'overdue' | 'upcoming' | 'completed'
}

/**
 * Hook for filtering unified training data on the client side
 * Works with merged training data from mergeAndSortCrewTrainingData
 */
export function useUnifiedTrainingFilters(opts: {
    initialFilter: UnifiedSearchFilter
    unifiedData: UnifiedTrainingData[]
}) {
    const { initialFilter, unifiedData } = opts

    // Add debugging to track where the filter state is coming from
    console.log('🔍 [useUnifiedTrainingFilters] Hook called with opts:', {
        initialFilter,
        initialFilterStringified: JSON.stringify(initialFilter),
        unifiedDataLength: unifiedData?.length || 0,
        stackTrace: new Error().stack?.split('\n').slice(1, 5).join('\n'),
    })

    const [filter, setFilter] = useState<UnifiedSearchFilter>(() => {
        console.log('🔍 [useUnifiedTrainingFilters] useState initializer called:', {
            initialFilter,
            initialFilterStringified: JSON.stringify(initialFilter),
            willUseEmptyObject: true,
        })
        return {}
    })

    // Debug initial filter state with detailed information
    console.log('🔍 [useUnifiedTrainingFilters] Hook initialized with detailed state:', {
        initialFilter,
        initialFilterStringified: JSON.stringify(initialFilter),
        actualInitialFilter: filter,
        actualFilterStringified: JSON.stringify(filter),
        hasMembers: !!filter.members,
        membersFilter: filter.members,
        membersFilterStringified: JSON.stringify(filter.members),
        initialFilterHasMembers: !!initialFilter.members,
        initialFilterMembersFilter: initialFilter.members,
    })

    const [debouncedFilter, setDebouncedFilter] = useState<UnifiedSearchFilter>(() => {
        console.log('🔍 [useUnifiedTrainingFilters] debouncedFilter useState initializer called:', {
            initialFilter,
            initialFilterStringified: JSON.stringify(initialFilter),
            willUseInitialFilter: true,
        })
        return initialFilter
    })

    // Debug debounced filter initialization
    console.log('🔍 [useUnifiedTrainingFilters] Debounced filter initialized:', {
        debouncedFilter,
        debouncedFilterStringified: JSON.stringify(debouncedFilter),
        hasDebouncedMembers: !!debouncedFilter.members,
        debouncedMembersFilter: debouncedFilter.members,
    })
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

    // Debounce filter changes to improve performance during rapid changes
    useEffect(() => {
        console.log('🔍 [useUnifiedTrainingFilters] Filter change detected, setting up debounce:', {
            currentFilter: filter,
            currentFilterStringified: JSON.stringify(filter),
            hasMembers: !!filter.members,
            membersFilter: filter.members,
        })

        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current)
        }

        debounceTimeoutRef.current = setTimeout(() => {
            console.log('🔍 [useUnifiedTrainingFilters] Debounce timeout triggered, updating debouncedFilter:', {
                newDebouncedFilter: filter,
                newDebouncedFilterStringified: JSON.stringify(filter),
                previousDebouncedFilter: debouncedFilter,
                previousDebouncedFilterStringified: JSON.stringify(debouncedFilter),
            })
            setDebouncedFilter(filter)
        }, 300) // 300ms debounce delay

        return () => {
            if (debounceTimeoutRef.current) {
                clearTimeout(debounceTimeoutRef.current)
            }
        }
    }, [filter, debouncedFilter])

    // Add useEffect to track filter state changes
    useEffect(() => {
        console.log('🔍 [useUnifiedTrainingFilters] Filter state changed:', {
            filter,
            filterStringified: JSON.stringify(filter),
            hasMembers: !!filter.members,
            membersFilter: filter.members,
            membersFilterStringified: JSON.stringify(filter.members),
            stackTrace: new Error().stack?.split('\n').slice(1, 8).join('\n'),
        })
    }, [filter])

    // Add useEffect to track debouncedFilter state changes
    useEffect(() => {
        console.log('🔍 [useUnifiedTrainingFilters] DebouncedFilter state changed:', {
            debouncedFilter,
            debouncedFilterStringified: JSON.stringify(debouncedFilter),
            hasDebouncedMembers: !!debouncedFilter.members,
            debouncedMembersFilter: debouncedFilter.members,
            debouncedMembersFilterStringified: JSON.stringify(debouncedFilter.members),
        })
    }, [debouncedFilter])

    const handleFilterChange = useCallback(
        ({ type, data }: { type: string; data: any }) => {
            console.log('🔍 [useUnifiedTrainingFilters] handleFilterChange called:', {
                type,
                data,
                dataType: typeof data,
                isArray: Array.isArray(data),
                dataLength: Array.isArray(data) ? data.length : 'N/A',
                currentFilter: filter,
                currentFilterStringified: JSON.stringify(filter),
                isCrewFilter: type === 'member',
            })

            const next: UnifiedSearchFilter = { ...filter }

            /* ---- vessel ------------------------------------------------------- */
            if (type === 'vessel') {
                if (Array.isArray(data) && data.length) {
                    next.vesselID = { in: data.map((d) => +d.value) }
                } else if (data && !Array.isArray(data)) {
                    next.vesselID = { eq: +data.value }
                } else {
                    delete next.vesselID
                }
            }

            /* ---- trainingType ------------------------------------------------- */
            if (type === 'trainingType') {
              

                if (Array.isArray(data) && data.length) {
                    const mappedValues = data.map((d) => +d.value)
                    next.trainingTypes = { id: { in: mappedValues } }
                    
                } else if (data && !Array.isArray(data)) {
                    const containsValue = +data.value
                    next.trainingTypes = { id: { contains: containsValue } }
                    
                } else {
                    delete next.trainingTypes
                    
                }
            }

            /* ---- trainer ------------------------------------------------------ */
            if (type === 'trainer') {
             
                if (Array.isArray(data) && data.length) {
                    const mappedValues = data.map((d) => +d.value)
                    next.trainer = { id: { in: mappedValues } }
                   
                } else if (data && !Array.isArray(data)) {
                    const eqValue = +data.value
                    next.trainer = { id: { eq: eqValue } }
                
                } else {
                    delete next.trainer
                  
                }
            }

            /* ---- member ------------------------------------------------------- */
            if (type === 'member') {
                console.log('🎯 [useUnifiedTrainingFilters] Processing member filter:', {
                    data,
                    dataType: typeof data,
                    isArray: Array.isArray(data),
                    dataLength: Array.isArray(data) ? data.length : 'N/A',
                    currentMembersFilter: filter.members,
                })

                if (Array.isArray(data) && data.length) {
                    const mappedValues = data.map((d) => +d.value)
                    next.members = { id: { in: mappedValues } }
                    console.log('🎯 [useUnifiedTrainingFilters] Set member filter (array):', {
                        mappedValues,
                        newFilter: next.members,
                    })
                } else if (data && !Array.isArray(data)) {
                    const eqValue = +data.value
                    next.members = { id: { eq: eqValue } }
                    console.log('🎯 [useUnifiedTrainingFilters] Set member filter (single):', {
                        eqValue,
                        newFilter: next.members,
                    })
                } else {
                    delete next.members
                    console.log('🎯 [useUnifiedTrainingFilters] Cleared member filter')
                }
            }

            /* ---- dateRange ---------------------------------------------------- */
            if (type === 'dateRange') {
                if (data?.startDate && data?.endDate) {
                    next.date = { gte: data.startDate, lte: data.endDate }
                } else {
                    delete next.date
                }
            }

            /* ---- category ----------------------------------------------------- */
            if (type === 'category') {
                if (data && data !== 'all') {
                    next.category = data
                } else {
                    delete next.category
                }
            }

            console.log('🔍 [useUnifiedTrainingFilters] Filter update complete:', {
                type,
                previousFilter: filter,
                newFilter: next,
                filterChanged: JSON.stringify(filter) !== JSON.stringify(next),
            })

            setFilter(next)
        },
        [filter],
    )

    // Performance-optimized client-side filtering of unified data
    const filteredData = useMemo(() => {
        console.log('🔍 [useUnifiedTrainingFilters] filteredData calculation triggered:', {
            unifiedDataLength: unifiedData?.length || 0,
            debouncedFilter,
            hasUnifiedData: !!unifiedData,
            isArray: Array.isArray(unifiedData),
        })

        if (!unifiedData || !Array.isArray(unifiedData)) {
            console.log('🔍 [useUnifiedTrainingFilters] No unified data available, returning empty array')
            return []
        }

        // Performance optimization: Check cache first (temporarily disabled for debugging)
        // const dataHash = generateDataHash(unifiedData)
        // const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash)
        // if (filterCache.has(cacheKey)) {
        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')
        //     return filterCache.get(cacheKey)!
        // }

        console.log('🔍 [useUnifiedTrainingFilters] Starting fresh filter calculation:', {
            totalItems: unifiedData.length,
            filter: debouncedFilter,
        })

        const startTime = performance.now()

        // Optimized filtering with early returns for better performance
        const filtered = unifiedData.filter((item: UnifiedTrainingData) => {
     

            // Category filter
            if (debouncedFilter.category && debouncedFilter.category !== 'all') {
                if (item.category !== debouncedFilter.category) {
             
                    return false
                }
            }

            // Vessel filter
            if (debouncedFilter.vesselID) {
                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers
                const itemVesselIdNum = typeof item.vesselID === 'string' ? parseInt(item.vesselID, 10) : item.vesselID

         

                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {
               
                    return false
                }
                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {
             
                    return false
                }
            }

            // Training type filter
            if (debouncedFilter.trainingTypes) {
                const trainingTypeId = item.trainingTypeID || item.trainingType?.id
                // Convert to number for comparison since filter values are numbers but data might be strings
                const trainingTypeIdNum = typeof trainingTypeId === 'string' ? parseInt(trainingTypeId, 10) : trainingTypeId

          

                if (debouncedFilter.trainingTypes.id?.contains && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {
            
                    return false
                }
                if (debouncedFilter.trainingTypes.id?.in && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {
              
                    return false
                }
            }

            // Trainer filter (for completed training sessions)
            if (debouncedFilter.trainer) {
           

                if (item.originalData) {
                    const trainerId = item.originalData.trainerID || item.originalData.trainer?.id
                    // Convert to number for comparison since filter values are numbers but data might be strings
                    const trainerIdNum = typeof trainerId === 'string' ? parseInt(trainerId, 10) : trainerId


                    if (trainerIdNum) {
                        if (debouncedFilter.trainer.id?.eq && trainerIdNum !== debouncedFilter.trainer.id.eq) {
                   
                            return false
                        }
                        if (debouncedFilter.trainer.id?.in && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {
                       
                            return false
                        }
                    } else if (debouncedFilter.trainer.id) {
                  
                        return false
                    }
                } else {
                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields
                    const itemAsAny = item as any // Type assertion to access potential trainer fields
              

                    // Check if item has trainer info in other fields
                    const itemTrainerId = itemAsAny.trainerID || itemAsAny.trainer?.id
                    const itemTrainerIdNum = typeof itemTrainerId === 'string' ? parseInt(itemTrainerId, 10) : itemTrainerId

                    if (itemTrainerIdNum) {
                        if (debouncedFilter.trainer.id?.eq && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {
                      
                            return false
                        }
                        if (debouncedFilter.trainer.id?.in && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {
                     
                            return false
                        }
                    } else if (debouncedFilter.trainer.id) {
                        // Special handling for overdue/upcoming training sessions:
                        // Include them in results since they don't have trainers assigned yet
                        if (item.category === 'overdue' || item.category === 'upcoming') {
                   
                            // Don't filter out overdue/upcoming items - they should be included
                        } else {
                  
                            return false
                        }
                    }
                }
            }

            // Member filter - only apply if explicitly set by user
            if (debouncedFilter.members && (debouncedFilter.members.id?.eq || debouncedFilter.members.id?.in)) {
                console.log('🎯 [useUnifiedTrainingFilters] Applying member filter to item:', {
                    itemId: item.id,
                    itemCategory: item.category,
                    itemMembers: item.members,
                    memberFilter: debouncedFilter.members,
                })

                if (item.members && item.members.length > 0) {
                    // Convert member IDs to numbers for consistent comparison
                    const memberIds = item.members.map((member: any) => {
                        const id = member.id
                        return typeof id === 'string' ? parseInt(id, 10) : id
                    })
                    console.log('🎯 [useUnifiedTrainingFilters] Item member IDs (converted to numbers):', memberIds)

                    if (debouncedFilter.members.id?.eq && !memberIds.includes(debouncedFilter.members.id.eq)) {
                        console.log('🎯 [useUnifiedTrainingFilters] Item filtered out (eq):', {
                            itemId: item.id,
                            expectedMemberId: debouncedFilter.members.id.eq,
                            actualMemberIds: memberIds,
                        })
                        return false
                    }
                    if (debouncedFilter.members.id?.in && debouncedFilter.members.id.in.length > 0) {
                        const hasMatchingMember = debouncedFilter.members.id.in.some(id => memberIds.includes(id))
                        if (!hasMatchingMember) {
                            console.log('🎯 [useUnifiedTrainingFilters] Item filtered out (in):', {
                                itemId: item.id,
                                expectedMemberIds: debouncedFilter.members.id.in,
                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map(id => `ID:${id}`),
                                actualMemberIds: memberIds,
                                actualMemberIdsDetailed: memberIds.map(id => `ID:${id}`),
                                intersection: memberIds.filter(id => debouncedFilter.members?.id?.in?.includes(id)),
                                dataTypeComparison: {
                                    expectedTypes: debouncedFilter.members.id.in.map(id => typeof id),
                                    actualTypes: memberIds.map(id => typeof id),
                                }
                            })
                            return false
                        } else {
                            console.log('🎯 [useUnifiedTrainingFilters] Item passed member filter (in):', {
                                itemId: item.id,
                                matchingMemberIds: debouncedFilter.members.id.in.filter(id => memberIds.includes(id)),
                            })
                        }
                    }
                } else {
                    // If item has no members but filter is set, filter it out
                    console.log('🎯 [useUnifiedTrainingFilters] Item has no members, filtering out:', {
                        itemId: item.id,
                        itemCategory: item.category,
                        memberFilter: debouncedFilter.members,
                    })
                    return false
                }
            }

            // Date filter
            if (debouncedFilter.date && item.dueDate) {
                const itemDate = new Date(item.dueDate)
                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {
                    return false
                }
                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {
                    return false
                }
            }

       
            return true
        })

        const endTime = performance.now()
        const filterTime = endTime - startTime

        console.log('🔍 [useUnifiedTrainingFilters] Filter calculation complete:', {
            originalCount: unifiedData.length,
            filteredCount: filtered.length,
            filterTime: `${filterTime.toFixed(2)}ms`,
            appliedFilters: Object.keys(debouncedFilter).filter(key => debouncedFilter[key as keyof UnifiedSearchFilter]),
            sampleFilteredItems: filtered.slice(0, 3).map(item => ({
                id: item.id,
                category: item.category,
                memberCount: item.members?.length || 0,
            })),
        })

        // Performance optimization: Cache the result (temporarily disabled for debugging)
        // if (filterCache.size >= CACHE_SIZE_LIMIT) {
        //     // Remove oldest entries when cache is full
        //     const firstKey = filterCache.keys().next().value
        //     if (firstKey) {
        //         filterCache.delete(firstKey)
        //     }
        // }
        // filterCache.set(cacheKey, filtered)

        return filtered
    }, [unifiedData, debouncedFilter])

    // Wrap setFilter to add debugging
    const debugSetFilter = useCallback((newFilter: UnifiedSearchFilter) => {
        console.log('🔍 [useUnifiedTrainingFilters] setFilter called:', {
            newFilter,
            newFilterStringified: JSON.stringify(newFilter),
            currentFilter: filter,
            currentFilterStringified: JSON.stringify(filter),
            stackTrace: new Error().stack?.split('\n').slice(1, 8).join('\n'),
        })
        setFilter(newFilter)
    }, [filter])

    return {
        filter,
        setFilter: debugSetFilter,
        handleFilterChange,
        filteredData
    }
}
