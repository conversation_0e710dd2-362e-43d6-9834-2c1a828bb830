"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-example.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingExample: function() { return /* binding */ UnifiedTrainingExample; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _unified_training_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./unified-training-table */ \"(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filter/unified-training-filter */ \"(app-pages-browser)/./src/components/filter/unified-training-filter.tsx\");\n/* harmony import */ var _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hooks/useUnifiedTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingExample,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UnifiedTrainingExample = (param)=>{\n    let { vesselId = 0, memberId = 0, isVesselView = false } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [completedTrainingList, setCompletedTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const includeCompleted = true // Always include completed in unified view\n    ;\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { getVesselWithIcon } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData)();\n    // Initialize permissions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.getPermissions);\n    }, []);\n    // Query for training session dues (overdue/upcoming)\n    const [queryTrainingSessionDues, { loading: duesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes || [];\n            setTrainingSessionDues(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading training session dues:\", error);\n        }\n    });\n    // Query for completed training sessions\n    const [queryCompletedTraining, { loading: completedLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_2__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes || [];\n            setCompletedTrainingList(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error loading completed training:\", error);\n        }\n    });\n    // Load training session dues function\n    const loadTrainingSessionDues = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (filter)=>{\n        const duesFilter = {};\n        if (memberId && memberId > 0) {\n            duesFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            duesFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            duesFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            duesFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            duesFilter.dueDate = filter.date;\n        } else {\n            duesFilter.dueDate = {\n                ne: null\n            };\n        }\n        await queryTrainingSessionDues({\n            variables: {\n                filter: duesFilter\n            }\n        });\n    }, [\n        memberId,\n        vesselId,\n        queryTrainingSessionDues\n    ]);\n    // Load completed training function\n    const loadTrainingList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (searchFilter.vesselID) {\n            completedFilter.vesselID = searchFilter.vesselID;\n        }\n        await queryCompletedTraining({\n            variables: {\n                filter: completedFilter,\n                offset: startPage * 20,\n                limit: 20\n            }\n        });\n    }, [\n        vesselId,\n        queryCompletedTraining\n    ]);\n    // Memoize the unified data calculation to prevent unnecessary recalculations\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_7__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Use unified training filters hook with client-side filtering\n    const { handleFilterChange, filteredData } = (0,_hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters)({\n        initialFilter: {},\n        unifiedData\n    });\n    // Memoize the filter change handler to prevent unnecessary re-renders\n    const memoizedHandleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((filterData)=>{\n        handleFilterChange(filterData);\n    }, [\n        handleFilterChange\n    ]);\n    // Simplified data loading without server-side filtering\n    const loadData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        // Load training session dues without filters (client-side filtering will handle this)\n        const duesFilter = {};\n        if (vesselId && vesselId > 0) {\n            duesFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (memberId && +memberId > 0) {\n            duesFilter.members = {\n                id: {\n                    contains: +memberId\n                }\n            };\n        }\n        await loadTrainingSessionDues(duesFilter);\n        // Load completed training without filters (client-side filtering will handle this)\n        const completedFilter = {};\n        if (vesselId && vesselId > 0) {\n            completedFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        await loadTrainingList(0, completedFilter);\n        setIsLoading(false);\n    }, [\n        vesselId,\n        memberId,\n        loadTrainingSessionDues,\n        loadTrainingList\n    ]);\n    // Load data on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        loadData\n    ]);\n    // Enhanced loading state management\n    const hasOverdueUpcomingData = trainingSessionDues && trainingSessionDues.length > 0;\n    const hasCompletedData = completedTrainingList && completedTrainingList.length > 0;\n    // Create detailed loading status\n    const getLoadingStatus = ()=>{\n        const statuses = [];\n        if (duesLoading) statuses.push(\"overdue/upcoming training\");\n        if (completedLoading) statuses.push(\"completed training\");\n        if (isLoading) statuses.push(\"training data\");\n        return statuses;\n    };\n    // Comprehensive loading component\n    const LoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm font-medium\",\n                                children: \"Loading training data...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 21\n                            }, undefined),\n                            status.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-muted-foreground\",\n                                children: [\n                                    \"Currently loading: \",\n                                    status.join(\", \")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 211,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 210,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Partial loading component for when some data is available\n    const PartialLoadingIndicator = (param)=>{\n        let { status } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center py-4 bg-muted/30 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: [\n                            \"Loading \",\n                            status.join(\", \"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 230,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 229,\n            columnNumber: 9\n        }, undefined);\n    };\n    // Check permissions\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_10__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 246,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n            lineNumber: 248,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_unified_training_filter__WEBPACK_IMPORTED_MODULE_8__.UnifiedTrainingFilter, {\n                    memberId: memberId,\n                    onChange: memoizedHandleFilterChange\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 255,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                className: \"p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_6__.H2, {\n                                        children: \"Crew Training Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 29\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground mt-1\",\n                                        children: \"Unified view of all training activities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: isLoading && !hasOverdueUpcomingData && !hasCompletedData ? // Full loading state when no data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingIndicator, {\n                                status: getLoadingStatus()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 29\n                            }, undefined) : isLoading && (hasOverdueUpcomingData || hasCompletedData) ? // Partial loading state when some data is available\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                        unifiedData: filteredData,\n                                        getVesselWithIcon: getVesselWithIcon,\n                                        includeCompleted: includeCompleted,\n                                        memberId: memberId,\n                                        isVesselView: isVesselView,\n                                        showToolbar: false,\n                                        pageSize: 20\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PartialLoadingIndicator, {\n                                        status: getLoadingStatus()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 29\n                            }, undefined) : // Normal state with data\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_unified_training_table__WEBPACK_IMPORTED_MODULE_3__.UnifiedTrainingTable, {\n                                unifiedData: filteredData,\n                                getVesselWithIcon: getVesselWithIcon,\n                                includeCompleted: includeCompleted,\n                                memberId: memberId,\n                                isVesselView: isVesselView,\n                                showToolbar: false,\n                                pageSize: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 21\n                        }, undefined),\n                        isLoading && (hasOverdueUpcomingData || hasCompletedData) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-muted-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Some data is still loading. The table will update automatically when new data becomes available.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 29\n                        }, undefined),\n                        !isLoading && unifiedData.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"No training data available\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs mt-1\",\n                                    children: \"Try adjusting your filters or refresh the data\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n                lineNumber: 262,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-example.tsx\",\n        lineNumber: 253,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedTrainingExample, \"uLhUAy/ItTes2fd28OrTN2BjAR0=\", false, function() {\n    return [\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_4__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_12__.useLazyQuery,\n        _hooks_useUnifiedTrainingFilters__WEBPACK_IMPORTED_MODULE_9__.useUnifiedTrainingFilters\n    ];\n});\n_c = UnifiedTrainingExample;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingExample);\nvar _c;\n$RefreshReg$(_c, \"UnifiedTrainingExample\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-example.tsx\n"));

/***/ })

});