"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        return {};\n    });\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        return initialFilter;\n    });\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter,\n        debouncedFilter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.members = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.members = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            return [];\n        }\n        // Performance optimization: Check cache first (temporarily disabled for debugging)\n        // const dataHash = generateDataHash(unifiedData)\n        // const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash)\n        // if (filterCache.has(cacheKey)) {\n        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')\n        //     return filterCache.get(cacheKey)!\n        // }\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                if (item.originalData) {\n                    var _item_originalData_trainer;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                        if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter - only apply if explicitly set by user\n            if (debouncedFilter.members && (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) || ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in))) {\n                if (item.members && item.members.length > 0) {\n                    var _debouncedFilter_members_id2, _debouncedFilter_members_id3;\n                    // Convert member IDs to numbers for consistent comparison\n                    const memberIds = item.members.map((member)=>{\n                        const id = member.id;\n                        return typeof id === \"string\" ? parseInt(id, 10) : id;\n                    });\n                    if (((_debouncedFilter_members_id2 = debouncedFilter.members.id) === null || _debouncedFilter_members_id2 === void 0 ? void 0 : _debouncedFilter_members_id2.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                        return false;\n                    }\n                    if (((_debouncedFilter_members_id3 = debouncedFilter.members.id) === null || _debouncedFilter_members_id3 === void 0 ? void 0 : _debouncedFilter_members_id3.in) && debouncedFilter.members.id.in.length > 0) {\n                        const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                        if (!hasMatchingMember) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (in):\", {\n                                itemId: item.id,\n                                expectedMemberIds: debouncedFilter.members.id.in,\n                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map((id)=>\"ID:\".concat(id)),\n                                actualMemberIds: memberIds,\n                                actualMemberIdsDetailed: memberIds.map((id)=>\"ID:\".concat(id)),\n                                intersection: memberIds.filter((id)=>{\n                                    var _debouncedFilter_members_id_in, _debouncedFilter_members_id, _debouncedFilter_members;\n                                    return (_debouncedFilter_members = debouncedFilter.members) === null || _debouncedFilter_members === void 0 ? void 0 : (_debouncedFilter_members_id = _debouncedFilter_members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : (_debouncedFilter_members_id_in = _debouncedFilter_members_id.in) === null || _debouncedFilter_members_id_in === void 0 ? void 0 : _debouncedFilter_members_id_in.includes(id);\n                                }),\n                                dataTypeComparison: {\n                                    expectedTypes: debouncedFilter.members.id.in.map((id)=>typeof id),\n                                    actualTypes: memberIds.map((id)=>typeof id)\n                                }\n                            });\n                            return false;\n                        } else {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item passed member filter (in):\", {\n                                itemId: item.id,\n                                matchingMemberIds: debouncedFilter.members.id.in.filter((id)=>memberIds.includes(id))\n                            });\n                        }\n                    }\n                } else {\n                    // If item has no members but filter is set, filter it out\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item has no members, filtering out:\", {\n                        itemId: item.id,\n                        itemCategory: item.category,\n                        memberFilter: debouncedFilter.members\n                    });\n                    return false;\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});