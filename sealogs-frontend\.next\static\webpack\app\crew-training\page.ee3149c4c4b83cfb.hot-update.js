"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/utils/crew-training-utils.ts ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _lib_actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * Optimized version with better error handling and performance\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    try {\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n            // Enhanced vessel data transformation with position information\n            let completeVesselData = training.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n                try {\n                    // Get complete vessel data including position, icon, and other metadata\n                    completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                    // Ensure we preserve original vessel data if transformation fails\n                    if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                        completeVesselData = training.vessel;\n                    }\n                    // Add position information if available\n                    if (training.vessel.position && !completeVesselData.position) {\n                        completeVesselData.position = training.vessel.position;\n                    }\n                    // Add location type if available\n                    if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                        completeVesselData.trainingLocationType = training.trainingLocationType;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                    completeVesselData = training.vessel;\n                }\n            }\n            // Enhanced member deduplication and normalization\n            const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n            const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n                // Check if member already exists in the accumulator\n                const existingMember = acc.find((m)=>m.id === member.id);\n                if (existingMember) {\n                    // Update existing member with more complete data\n                    existingMember.firstName = member.firstName || existingMember.firstName;\n                    existingMember.surname = member.surname || existingMember.surname;\n                    existingMember.email = member.email || existingMember.email;\n                } else {\n                    // Add new member with normalized data\n                    acc.push({\n                        id: member.id,\n                        firstName: member.firstName || \"\",\n                        surname: member.surname || \"\",\n                        email: member.email || \"\",\n                        ...member // Preserve any additional member data\n                    });\n                }\n                return acc;\n            }, []);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n                vessel: completeVesselData,\n                trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: deduplicatedMembers,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                category: \"completed\",\n                originalData: training\n            };\n        });\n    } catch (error) {\n        console.error(\"Error transforming completed training data:\", error);\n        return [];\n    }\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        return [];\n    }\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item but log it\n            if (!hasValidVesselMembers) {\n                var _item_vessel_seaLogsMembers_nodes1, _item_vessel_seaLogsMembers1, _item_vessel1;\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Item without vessel member match:\", {\n                    memberID: item.memberID,\n                    vesselID: item.vesselID,\n                    vesselMembers: (_item_vessel1 = item.vessel) === null || _item_vessel1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers1 = _item_vessel1.seaLogsMembers) === null || _item_vessel_seaLogsMembers1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes1 = _item_vessel_seaLogsMembers1.nodes) === null || _item_vessel_seaLogsMembers_nodes1 === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes1.map((m)=>m === null || m === void 0 ? void 0 : m.id)\n                });\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n            originalCount: trainingSessionDues.length,\n            filteredCount: filteredData.length,\n            removedCount: trainingSessionDues.length - filteredData.length\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_lib_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n            totalRecords: dueWithStatus.length,\n            statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n                const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n                acc[key] = (acc[key] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n            groupCount: Object.keys(groupedDues).length,\n            groupKeys: Object.keys(groupedDues),\n            sampleGroup: Object.values(groupedDues)[0]\n        });\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting member merging for groups:\", Object.values(groupedDues).length);\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_members;\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing group:\", {\n                id: group.id,\n                vesselID: group.vesselID,\n                trainingTypeID: group.trainingTypeID,\n                membersCount: ((_group_members = group.members) === null || _group_members === void 0 ? void 0 : _group_members.length) || 0,\n                members: group.members\n            });\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing members for group:\", group.members);\n            group.members.forEach((member)=>{\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing member:\", member);\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                } else {\n                    console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Invalid member:\", member);\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Merged members result:\", mergedMembers);\n            try {\n                var _group_status, _group_status1;\n                // Determine category based on status\n                let category;\n                if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                    category = \"overdue\";\n                } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                    category = \"upcoming\";\n                } else {\n                    category = \"upcoming\" // Default for future due dates\n                    ;\n                }\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Determined category:\", category, \"for status:\", group.status);\n                // Enhanced vessel data with position information\n                // Create a new object to avoid \"object is not extensible\" errors\n                const enhancedVessel = {\n                    ...group.vessel || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    // Add training location type if available\n                    ...group.trainingLocationType && {\n                        trainingLocationType: group.trainingLocationType\n                    }\n                };\n                const result = {\n                    id: group.id,\n                    dueDate: group.dueDate,\n                    vesselID: group.vesselID,\n                    vessel: enhancedVessel,\n                    trainingTypeID: group.trainingTypeID,\n                    trainingType: group.trainingType || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    members: mergedMembers,\n                    status: group.status,\n                    category,\n                    originalData: group\n                };\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Created unified record:\", result);\n                return result;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error creating unified record:\", error, \"for group:\", group);\n                return null // Return null to filter out failed records\n                ;\n            }\n        }).filter(Boolean)// Filter out null values and cast\n        ;\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0],\n            allRecords: mergedDues\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for deduplication\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting deduplication:\", {\n        totalRecords: data.length,\n        sampleRecord: data[0]\n    });\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            // Handle both string and number IDs\n            const itemId = typeof item.id === \"string\" ? parseInt(item.id, 10) : item.id;\n            if (!item || !itemId && itemId !== 0 || isNaN(itemId)) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(itemId)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(itemId);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(itemId, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Deduplication complete:\", {\n            originalCount: data.length,\n            deduplicatedCount: deduplicatedData.length,\n            removedDuplicates: data.length - deduplicatedData.length,\n            categoryBreakdown: deduplicatedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * Optimized version with better performance and error handling\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for sorting\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting sort:\", {\n        totalRecords: data.length,\n        categories: data.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    try {\n        return data.sort((a, b)=>{\n            try {\n                // First sort by priority (overdue > upcoming > completed)\n                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n                if (priorityDiff !== 0) {\n                    return priorityDiff;\n                }\n                // Within same priority, sort by date with error handling\n                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;\n                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;\n                // Handle invalid dates\n                if (isNaN(dateA) && isNaN(dateB)) return 0;\n                if (isNaN(dateA)) return 1;\n                if (isNaN(dateB)) return -1;\n                if (a.category === \"overdue\") {\n                    // For overdue: most overdue first (earliest due date first)\n                    return dateA - dateB;\n                } else if (a.category === \"upcoming\") {\n                    // For upcoming: soonest due date first\n                    return dateA - dateB;\n                } else {\n                    // For completed: most recent completion first (latest date first)\n                    return dateB - dateA;\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error comparing items in sort:\", error, {\n                    a,\n                    b\n                });\n                return 0 // Keep original order if comparison fails\n                ;\n            }\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in sorting:\", error);\n        return data // Return original data if sorting fails\n        ;\n    }\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Sort with priority-based ordering (deduplication removed)\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\n"));

/***/ })

});