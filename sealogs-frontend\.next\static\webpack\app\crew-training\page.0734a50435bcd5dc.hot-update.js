"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing trainingType filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataValue: data === null || data === void 0 ? void 0 : data.value,\n                currentTrainingTypes: filter.trainingTypes\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (array):\", {\n                    mappedValues,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (single):\", {\n                    containsValue,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else {\n                delete next.trainingTypes;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Deleted trainingTypes filter\");\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing trainer filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataValue: data === null || data === void 0 ? void 0 : data.value,\n                currentTrainer: filter.trainer\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainer (array):\", {\n                    mappedValues,\n                    nextTrainer: next.trainer\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainer (single):\", {\n                    eqValue,\n                    nextTrainer: next.trainer\n                });\n            } else {\n                delete next.trainer;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Deleted trainer filter\");\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Final filter state:\", {\n            type,\n            previousFilter: filter,\n            nextFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDE80 [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting filter operation:\", {\n            unifiedDataLength: unifiedData.length,\n            debouncedFilter,\n            hasTrainingTypeFilter: !!debouncedFilter.trainingTypes\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _item_trainingType, _item_trainingType1;\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering item:\", {\n                itemId: item.id,\n                category: item.category,\n                trainingType: (_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title,\n                vesselID: item.vesselID,\n                hasOriginalData: !!item.originalData,\n                originalDataKeys: item.originalData ? Object.keys(item.originalData) : [],\n                originalDataSample: item.originalData ? {\n                    trainerID: item.originalData.trainerID,\n                    trainer: item.originalData.trainer,\n                    hasTrainer: \"trainer\" in item.originalData,\n                    hasTrainerID: \"trainerID\" in item.originalData\n                } : null,\n                debouncedFilter\n            });\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by category:\", {\n                        itemCategory: item.category,\n                        filterCategory: debouncedFilter.category\n                    });\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Vessel filter check:\", {\n                    itemVesselID: item.vesselID,\n                    itemVesselIdNum,\n                    filterVesselID: debouncedFilter.vesselID,\n                    itemVesselIDType: typeof item.vesselID\n                });\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by vessel eq:\", {\n                        itemVesselID: item.vesselID,\n                        itemVesselIdNum,\n                        expectedVesselID: debouncedFilter.vesselID.eq\n                    });\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by vessel in:\", {\n                        itemVesselID: item.vesselID,\n                        itemVesselIdNum,\n                        expectedVesselIDs: debouncedFilter.vesselID.in\n                    });\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType2, _item_trainingType3, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1, _debouncedFilter_trainingTypes_id2, _debouncedFilter_trainingTypes_id3;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType2 = item.trainingType) === null || _item_trainingType2 === void 0 ? void 0 : _item_trainingType2.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Training type filter check:\", {\n                    itemId: item.id,\n                    trainingTypeTitle: (_item_trainingType3 = item.trainingType) === null || _item_trainingType3 === void 0 ? void 0 : _item_trainingType3.title,\n                    trainingTypeId,\n                    trainingTypeIdNum,\n                    filterContains: (_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains,\n                    filterIn: (_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in,\n                    item: {\n                        trainingTypeID: item.trainingTypeID,\n                        trainingType: item.trainingType,\n                        category: item.category\n                    }\n                });\n                if (((_debouncedFilter_trainingTypes_id2 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id2 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id2.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by contains check:\", {\n                        itemId: item.id,\n                        trainingTypeIdNum,\n                        expectedContains: debouncedFilter.trainingTypes.id.contains\n                    });\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id3 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id3 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id3.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by in check:\", {\n                        itemId: item.id,\n                        trainingTypeIdNum,\n                        expectedIn: debouncedFilter.trainingTypes.id.in\n                    });\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Trainer filter check:\", {\n                    itemId: item.id,\n                    category: item.category,\n                    hasOriginalData: !!item.originalData,\n                    originalData: item.originalData,\n                    filterTrainer: debouncedFilter.trainer,\n                    itemType: item.category\n                });\n                if (item.originalData) {\n                    var _item_originalData_trainer, _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Trainer ID check:\", {\n                        itemId: item.id,\n                        trainerId,\n                        trainerIdNum,\n                        trainerIdType: typeof trainerId,\n                        filterEq: (_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq,\n                        filterIn: (_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in,\n                        originalDataKeys: Object.keys(item.originalData || {}),\n                        trainerData: item.originalData.trainer\n                    });\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer eq:\", {\n                                itemId: item.id,\n                                trainerIdNum,\n                                expectedTrainerID: debouncedFilter.trainer.id.eq\n                            });\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer in:\", {\n                                itemId: item.id,\n                                trainerIdNum,\n                                expectedTrainerIDs: debouncedFilter.trainer.id.in\n                            });\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // If trainer filter is applied but no trainer data exists, exclude this item\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out - no trainer data:\", {\n                            itemId: item.id,\n                            category: item.category,\n                            hasOriginalData: !!item.originalData,\n                            originalDataKeys: Object.keys(item.originalData || {})\n                        });\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] No originalData, checking other trainer fields:\", {\n                        itemId: item.id,\n                        category: item.category,\n                        itemKeys: Object.keys(item),\n                        hasTrainerField: \"trainer\" in itemAsAny,\n                        hasTrainerID: \"trainerID\" in itemAsAny,\n                        trainer: itemAsAny.trainer,\n                        trainerID: itemAsAny.trainerID\n                    });\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id4, _debouncedFilter_trainer_id5;\n                        if (((_debouncedFilter_trainer_id4 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id4 === void 0 ? void 0 : _debouncedFilter_trainer_id4.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer eq (no originalData):\", {\n                                itemId: item.id,\n                                itemTrainerIdNum,\n                                expectedTrainerID: debouncedFilter.trainer.id.eq\n                            });\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id5 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id5 === void 0 ? void 0 : _debouncedFilter_trainer_id5.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer in (no originalData):\", {\n                                itemId: item.id,\n                                itemTrainerIdNum,\n                                expectedTrainerIDs: debouncedFilter.trainer.id.in\n                            });\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // If trainer filter is applied but no trainer data exists anywhere, exclude this item\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out - no trainer data anywhere:\", {\n                            itemId: item.id,\n                            category: item.category\n                        });\n                        return false;\n                    }\n                }\n            }\n            // Member filter\n            if (debouncedFilter.members && item.members) {\n                var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                    return false;\n                }\n                if ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in) {\n                    const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item passed all filters:\", {\n                itemId: item.id,\n                category: item.category,\n                trainingType: (_item_trainingType1 = item.trainingType) === null || _item_trainingType1 === void 0 ? void 0 : _item_trainingType1.title\n            });\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter operation completed:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: debouncedFilter,\n            filteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_trainingType;\n                return {\n                    id: item.id,\n                    trainingType: (_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title,\n                    category: item.category,\n                    trainingTypeID: item.trainingTypeID\n                };\n            })\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});