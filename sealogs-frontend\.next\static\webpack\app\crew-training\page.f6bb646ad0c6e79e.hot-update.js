"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/utils/crew-training-utils.ts ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _lib_actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * Optimized version with better error handling and performance\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    try {\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n            // Enhanced vessel data transformation with position information\n            let completeVesselData = training.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n                try {\n                    // Get complete vessel data including position, icon, and other metadata\n                    completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                    // Ensure we preserve original vessel data if transformation fails\n                    if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                        completeVesselData = training.vessel;\n                    }\n                    // Add position information if available\n                    if (training.vessel.position && !completeVesselData.position) {\n                        completeVesselData.position = training.vessel.position;\n                    }\n                    // Add location type if available\n                    if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                        completeVesselData.trainingLocationType = training.trainingLocationType;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                    completeVesselData = training.vessel;\n                }\n            }\n            // Enhanced member deduplication and normalization\n            const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n            const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n                // Check if member already exists in the accumulator\n                const existingMember = acc.find((m)=>m.id === member.id);\n                if (existingMember) {\n                    // Update existing member with more complete data\n                    existingMember.firstName = member.firstName || existingMember.firstName;\n                    existingMember.surname = member.surname || existingMember.surname;\n                    existingMember.email = member.email || existingMember.email;\n                } else {\n                    // Add new member with normalized data\n                    acc.push({\n                        id: member.id,\n                        firstName: member.firstName || \"\",\n                        surname: member.surname || \"\",\n                        email: member.email || \"\",\n                        ...member // Preserve any additional member data\n                    });\n                }\n                return acc;\n            }, []);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n                vessel: completeVesselData,\n                trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: deduplicatedMembers,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                category: \"completed\",\n                originalData: training\n            };\n        });\n    } catch (error) {\n        console.error(\"Error transforming completed training data:\", error);\n        return [];\n    }\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        return [];\n    }\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item\n            if (!hasValidVesselMembers) {\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_lib_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_members;\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing group:\", {\n                id: group.id,\n                vesselID: group.vesselID,\n                trainingTypeID: group.trainingTypeID,\n                membersCount: ((_group_members = group.members) === null || _group_members === void 0 ? void 0 : _group_members.length) || 0,\n                members: group.members\n            });\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing members for group:\", group.members);\n            group.members.forEach((member)=>{\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing member:\", member);\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                } else {\n                    console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Invalid member:\", member);\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Merged members result:\", mergedMembers);\n            try {\n                var _group_status, _group_status1;\n                // Determine category based on status\n                let category;\n                if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                    category = \"overdue\";\n                } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                    category = \"upcoming\";\n                } else {\n                    category = \"upcoming\" // Default for future due dates\n                    ;\n                }\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Determined category:\", category, \"for status:\", group.status);\n                // Enhanced vessel data with position information\n                // Create a new object to avoid \"object is not extensible\" errors\n                const enhancedVessel = {\n                    ...group.vessel || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    // Add training location type if available\n                    ...group.trainingLocationType && {\n                        trainingLocationType: group.trainingLocationType\n                    }\n                };\n                const result = {\n                    id: group.id,\n                    dueDate: group.dueDate,\n                    vesselID: group.vesselID,\n                    vessel: enhancedVessel,\n                    trainingTypeID: group.trainingTypeID,\n                    trainingType: group.trainingType || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    members: mergedMembers,\n                    status: group.status,\n                    category,\n                    originalData: group\n                };\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Created unified record:\", result);\n                return result;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error creating unified record:\", error, \"for group:\", group);\n                return null // Return null to filter out failed records\n                ;\n            }\n        }).filter(Boolean)// Filter out null values and cast\n        ;\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0],\n            allRecords: mergedDues\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for deduplication\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting deduplication:\", {\n        totalRecords: data.length,\n        sampleRecord: data[0]\n    });\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            // Handle both string and number IDs\n            const itemId = typeof item.id === \"string\" ? parseInt(item.id, 10) : item.id;\n            if (!item || !itemId && itemId !== 0 || isNaN(itemId)) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(itemId)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(itemId);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(itemId, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Deduplication complete:\", {\n            originalCount: data.length,\n            deduplicatedCount: deduplicatedData.length,\n            removedDuplicates: data.length - deduplicatedData.length,\n            categoryBreakdown: deduplicatedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * Optimized version with better performance and error handling\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for sorting\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting sort:\", {\n        totalRecords: data.length,\n        categories: data.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    try {\n        return data.sort((a, b)=>{\n            try {\n                // First sort by priority (overdue > upcoming > completed)\n                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n                if (priorityDiff !== 0) {\n                    return priorityDiff;\n                }\n                // Within same priority, sort by date with error handling\n                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;\n                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;\n                // Handle invalid dates\n                if (isNaN(dateA) && isNaN(dateB)) return 0;\n                if (isNaN(dateA)) return 1;\n                if (isNaN(dateB)) return -1;\n                if (a.category === \"overdue\") {\n                    // For overdue: most overdue first (earliest due date first)\n                    return dateA - dateB;\n                } else if (a.category === \"upcoming\") {\n                    // For upcoming: soonest due date first\n                    return dateA - dateB;\n                } else {\n                    // For completed: most recent completion first (latest date first)\n                    return dateB - dateA;\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error comparing items in sort:\", error, {\n                    a,\n                    b\n                });\n                return 0 // Keep original order if comparison fails\n                ;\n            }\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in sorting:\", error);\n        return data // Return original data if sorting fails\n        ;\n    }\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Sort with priority-based ordering (deduplication removed)\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\n"));

/***/ })

});