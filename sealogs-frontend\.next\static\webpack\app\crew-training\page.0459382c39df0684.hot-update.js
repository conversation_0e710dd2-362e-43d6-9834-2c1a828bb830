"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    // Debug initial filter state with detailed information\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Hook initialized with detailed state:\", {\n        initialFilter,\n        initialFilterStringified: JSON.stringify(initialFilter),\n        actualInitialFilter: filter,\n        actualFilterStringified: JSON.stringify(filter),\n        hasMembers: !!filter.members,\n        membersFilter: filter.members,\n        membersFilterStringified: JSON.stringify(filter.members),\n        initialFilterHasMembers: !!initialFilter.members,\n        initialFilterMembersFilter: initialFilter.members\n    });\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    // Debug debounced filter initialization\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounced filter initialized:\", {\n        debouncedFilter,\n        debouncedFilterStringified: JSON.stringify(debouncedFilter),\n        hasDebouncedMembers: !!debouncedFilter.members,\n        debouncedMembersFilter: debouncedFilter.members\n    });\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter change detected, setting up debounce:\", {\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            hasMembers: !!filter.members,\n            membersFilter: filter.members\n        });\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounce timeout triggered, updating debouncedFilter:\", {\n                newDebouncedFilter: filter,\n                newDebouncedFilterStringified: JSON.stringify(filter),\n                previousDebouncedFilter: debouncedFilter,\n                previousDebouncedFilterStringified: JSON.stringify(debouncedFilter)\n            });\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter,\n        debouncedFilter\n    ]);\n    // Add useEffect to track filter state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter state changed:\", {\n            filter,\n            filterStringified: JSON.stringify(filter),\n            hasMembers: !!filter.members,\n            membersFilter: filter.members,\n            membersFilterStringified: JSON.stringify(filter.members)\n        });\n    }, [\n        filter\n    ]);\n    // Add useEffect to track debouncedFilter state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] DebouncedFilter state changed:\", {\n            debouncedFilter,\n            debouncedFilterStringified: JSON.stringify(debouncedFilter),\n            hasDebouncedMembers: !!debouncedFilter.members,\n            debouncedMembersFilter: debouncedFilter.members,\n            debouncedMembersFilterStringified: JSON.stringify(debouncedFilter.members)\n        });\n    }, [\n        debouncedFilter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            dataLength: Array.isArray(data) ? data.length : \"N/A\",\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            isCrewFilter: type === \"member\"\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing member filter:\", {\n                data,\n                dataType: typeof data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                currentMembersFilter: filter.members\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.members = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (array):\", {\n                    mappedValues,\n                    newFilter: next.members\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.members = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (single):\", {\n                    eqValue,\n                    newFilter: next.members\n                });\n            } else {\n                delete next.members;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Cleared member filter\");\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter update complete:\", {\n            type,\n            previousFilter: filter,\n            newFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] filteredData calculation triggered:\", {\n            unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n            debouncedFilter,\n            hasUnifiedData: !!unifiedData,\n            isArray: Array.isArray(unifiedData)\n        });\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data available, returning empty array\");\n            return [];\n        }\n        // Performance optimization: Check cache first (temporarily disabled for debugging)\n        // const dataHash = generateDataHash(unifiedData)\n        // const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash)\n        // if (filterCache.has(cacheKey)) {\n        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')\n        //     return filterCache.get(cacheKey)!\n        // }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting fresh filter calculation:\", {\n            totalItems: unifiedData.length,\n            filter: debouncedFilter\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                if (item.originalData) {\n                    var _item_originalData_trainer;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                        if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter - only apply if explicitly set by user\n            if (debouncedFilter.members && (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) || ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in))) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Applying member filter to item:\", {\n                    itemId: item.id,\n                    itemCategory: item.category,\n                    itemMembers: item.members,\n                    memberFilter: debouncedFilter.members\n                });\n                if (item.members && item.members.length > 0) {\n                    var _debouncedFilter_members_id2, _debouncedFilter_members_id3;\n                    const memberIds = item.members.map((member)=>member.id);\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item member IDs:\", memberIds);\n                    if (((_debouncedFilter_members_id2 = debouncedFilter.members.id) === null || _debouncedFilter_members_id2 === void 0 ? void 0 : _debouncedFilter_members_id2.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (eq):\", {\n                            itemId: item.id,\n                            expectedMemberId: debouncedFilter.members.id.eq,\n                            actualMemberIds: memberIds\n                        });\n                        return false;\n                    }\n                    if (((_debouncedFilter_members_id3 = debouncedFilter.members.id) === null || _debouncedFilter_members_id3 === void 0 ? void 0 : _debouncedFilter_members_id3.in) && debouncedFilter.members.id.in.length > 0) {\n                        const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                        if (!hasMatchingMember) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (in):\", {\n                                itemId: item.id,\n                                expectedMemberIds: debouncedFilter.members.id.in,\n                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map((id)=>\"ID:\".concat(id)),\n                                actualMemberIds: memberIds,\n                                actualMemberIdsDetailed: memberIds.map((id)=>\"ID:\".concat(id)),\n                                intersection: memberIds.filter((id)=>{\n                                    var _debouncedFilter_members_id_in, _debouncedFilter_members_id, _debouncedFilter_members;\n                                    return (_debouncedFilter_members = debouncedFilter.members) === null || _debouncedFilter_members === void 0 ? void 0 : (_debouncedFilter_members_id = _debouncedFilter_members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : (_debouncedFilter_members_id_in = _debouncedFilter_members_id.in) === null || _debouncedFilter_members_id_in === void 0 ? void 0 : _debouncedFilter_members_id_in.includes(id);\n                                })\n                            });\n                            return false;\n                        } else {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item passed member filter (in):\", {\n                                itemId: item.id,\n                                matchingMemberIds: debouncedFilter.members.id.in.filter((id)=>memberIds.includes(id))\n                            });\n                        }\n                    }\n                } else {\n                    // If item has no members but filter is set, filter it out\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item has no members, filtering out:\", {\n                        itemId: item.id,\n                        itemCategory: item.category,\n                        memberFilter: debouncedFilter.members\n                    });\n                    return false;\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter calculation complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key]),\n            sampleFilteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_members;\n                return {\n                    id: item.id,\n                    category: item.category,\n                    memberCount: ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.length) || 0\n                };\n            })\n        });\n        // Performance optimization: Cache the result (temporarily disabled for debugging)\n        // if (filterCache.size >= CACHE_SIZE_LIMIT) {\n        //     // Remove oldest entries when cache is full\n        //     const firstKey = filterCache.keys().next().value\n        //     if (firstKey) {\n        //         filterCache.delete(firstKey)\n        //     }\n        // }\n        // filterCache.set(cacheKey, filtered)\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});