"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/utils/crew-training-utils.ts ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _lib_actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * Optimized version with better error handling and performance\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    try {\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n            // Enhanced vessel data transformation with position information\n            let completeVesselData = training.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n                try {\n                    // Get complete vessel data including position, icon, and other metadata\n                    completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                    // Ensure we preserve original vessel data if transformation fails\n                    if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                        completeVesselData = training.vessel;\n                    }\n                    // Add position information if available\n                    if (training.vessel.position && !completeVesselData.position) {\n                        completeVesselData.position = training.vessel.position;\n                    }\n                    // Add location type if available\n                    if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                        completeVesselData.trainingLocationType = training.trainingLocationType;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                    completeVesselData = training.vessel;\n                }\n            }\n            // Enhanced member deduplication and normalization\n            const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n            const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n                // Check if member already exists in the accumulator\n                const existingMember = acc.find((m)=>m.id === member.id);\n                if (existingMember) {\n                    // Update existing member with more complete data\n                    existingMember.firstName = member.firstName || existingMember.firstName;\n                    existingMember.surname = member.surname || existingMember.surname;\n                    existingMember.email = member.email || existingMember.email;\n                } else {\n                    // Add new member with normalized data\n                    acc.push({\n                        id: member.id,\n                        firstName: member.firstName || \"\",\n                        surname: member.surname || \"\",\n                        email: member.email || \"\",\n                        ...member // Preserve any additional member data\n                    });\n                }\n                return acc;\n            }, []);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n                vessel: completeVesselData,\n                trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: deduplicatedMembers,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                category: \"completed\",\n                originalData: training\n            };\n        });\n    } catch (error) {\n        console.error(\"Error transforming completed training data:\", error);\n        return [];\n    }\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        return [];\n    }\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item\n            if (!hasValidVesselMembers) {\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_lib_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n            groupCount: Object.keys(groupedDues).length,\n            groupKeys: Object.keys(groupedDues),\n            sampleGroup: Object.values(groupedDues)[0]\n        });\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting member merging for groups:\", Object.values(groupedDues).length);\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_members;\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing group:\", {\n                id: group.id,\n                vesselID: group.vesselID,\n                trainingTypeID: group.trainingTypeID,\n                membersCount: ((_group_members = group.members) === null || _group_members === void 0 ? void 0 : _group_members.length) || 0,\n                members: group.members\n            });\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing members for group:\", group.members);\n            group.members.forEach((member)=>{\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing member:\", member);\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                } else {\n                    console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Invalid member:\", member);\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Merged members result:\", mergedMembers);\n            try {\n                var _group_status, _group_status1;\n                // Determine category based on status\n                let category;\n                if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                    category = \"overdue\";\n                } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                    category = \"upcoming\";\n                } else {\n                    category = \"upcoming\" // Default for future due dates\n                    ;\n                }\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Determined category:\", category, \"for status:\", group.status);\n                // Enhanced vessel data with position information\n                // Create a new object to avoid \"object is not extensible\" errors\n                const enhancedVessel = {\n                    ...group.vessel || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    // Add training location type if available\n                    ...group.trainingLocationType && {\n                        trainingLocationType: group.trainingLocationType\n                    }\n                };\n                const result = {\n                    id: group.id,\n                    dueDate: group.dueDate,\n                    vesselID: group.vesselID,\n                    vessel: enhancedVessel,\n                    trainingTypeID: group.trainingTypeID,\n                    trainingType: group.trainingType || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    members: mergedMembers,\n                    status: group.status,\n                    category,\n                    originalData: group\n                };\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Created unified record:\", result);\n                return result;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error creating unified record:\", error, \"for group:\", group);\n                return null // Return null to filter out failed records\n                ;\n            }\n        }).filter(Boolean)// Filter out null values and cast\n        ;\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0],\n            allRecords: mergedDues\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for deduplication\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting deduplication:\", {\n        totalRecords: data.length,\n        sampleRecord: data[0]\n    });\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            // Handle both string and number IDs\n            const itemId = typeof item.id === \"string\" ? parseInt(item.id, 10) : item.id;\n            if (!item || !itemId && itemId !== 0 || isNaN(itemId)) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(itemId)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(itemId);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(itemId, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Deduplication complete:\", {\n            originalCount: data.length,\n            deduplicatedCount: deduplicatedData.length,\n            removedDuplicates: data.length - deduplicatedData.length,\n            categoryBreakdown: deduplicatedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * Optimized version with better performance and error handling\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for sorting\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting sort:\", {\n        totalRecords: data.length,\n        categories: data.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    try {\n        return data.sort((a, b)=>{\n            try {\n                // First sort by priority (overdue > upcoming > completed)\n                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n                if (priorityDiff !== 0) {\n                    return priorityDiff;\n                }\n                // Within same priority, sort by date with error handling\n                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;\n                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;\n                // Handle invalid dates\n                if (isNaN(dateA) && isNaN(dateB)) return 0;\n                if (isNaN(dateA)) return 1;\n                if (isNaN(dateB)) return -1;\n                if (a.category === \"overdue\") {\n                    // For overdue: most overdue first (earliest due date first)\n                    return dateA - dateB;\n                } else if (a.category === \"upcoming\") {\n                    // For upcoming: soonest due date first\n                    return dateA - dateB;\n                } else {\n                    // For completed: most recent completion first (latest date first)\n                    return dateB - dateA;\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error comparing items in sort:\", error, {\n                    a,\n                    b\n                });\n                return 0 // Keep original order if comparison fails\n                ;\n            }\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in sorting:\", error);\n        return data // Return original data if sorting fails\n        ;\n    }\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Sort with priority-based ordering (deduplication removed)\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\n"));

/***/ })

});