"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    var _stack;\n    const { initialFilter, unifiedData } = opts;\n    // Add debugging to track where the filter state is coming from\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Hook called with opts:\", {\n        initialFilter,\n        initialFilterStringified: JSON.stringify(initialFilter),\n        unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n        stackTrace: (_stack = new Error().stack) === null || _stack === void 0 ? void 0 : _stack.split(\"\\n\").slice(1, 5).join(\"\\n\")\n    });\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] useState initializer called:\", {\n            initialFilter,\n            initialFilterStringified: JSON.stringify(initialFilter),\n            willUseEmptyObject: true\n        });\n        return {};\n    });\n    // Debug initial filter state with detailed information\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Hook initialized with detailed state:\", {\n        initialFilter,\n        initialFilterStringified: JSON.stringify(initialFilter),\n        actualInitialFilter: filter,\n        actualFilterStringified: JSON.stringify(filter),\n        hasMembers: !!filter.members,\n        membersFilter: filter.members,\n        membersFilterStringified: JSON.stringify(filter.members),\n        initialFilterHasMembers: !!initialFilter.members,\n        initialFilterMembersFilter: initialFilter.members\n    });\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] debouncedFilter useState initializer called:\", {\n            initialFilter,\n            initialFilterStringified: JSON.stringify(initialFilter),\n            willUseInitialFilter: true\n        });\n        return initialFilter;\n    });\n    // Debug debounced filter initialization\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounced filter initialized:\", {\n        debouncedFilter,\n        debouncedFilterStringified: JSON.stringify(debouncedFilter),\n        hasDebouncedMembers: !!debouncedFilter.members,\n        debouncedMembersFilter: debouncedFilter.members\n    });\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter change detected, setting up debounce:\", {\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            hasMembers: !!filter.members,\n            membersFilter: filter.members\n        });\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounce timeout triggered, updating debouncedFilter:\", {\n                newDebouncedFilter: filter,\n                newDebouncedFilterStringified: JSON.stringify(filter),\n                previousDebouncedFilter: debouncedFilter,\n                previousDebouncedFilterStringified: JSON.stringify(debouncedFilter)\n            });\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter,\n        debouncedFilter\n    ]);\n    // Add useEffect to track filter state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter state changed:\", {\n            filter,\n            filterStringified: JSON.stringify(filter),\n            hasMembers: !!filter.members,\n            membersFilter: filter.members,\n            membersFilterStringified: JSON.stringify(filter.members)\n        });\n    }, [\n        filter\n    ]);\n    // Add useEffect to track debouncedFilter state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] DebouncedFilter state changed:\", {\n            debouncedFilter,\n            debouncedFilterStringified: JSON.stringify(debouncedFilter),\n            hasDebouncedMembers: !!debouncedFilter.members,\n            debouncedMembersFilter: debouncedFilter.members,\n            debouncedMembersFilterStringified: JSON.stringify(debouncedFilter.members)\n        });\n    }, [\n        debouncedFilter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            dataLength: Array.isArray(data) ? data.length : \"N/A\",\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            isCrewFilter: type === \"member\"\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing member filter:\", {\n                data,\n                dataType: typeof data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                currentMembersFilter: filter.members\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.members = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (array):\", {\n                    mappedValues,\n                    newFilter: next.members\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.members = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (single):\", {\n                    eqValue,\n                    newFilter: next.members\n                });\n            } else {\n                delete next.members;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Cleared member filter\");\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter update complete:\", {\n            type,\n            previousFilter: filter,\n            newFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] filteredData calculation triggered:\", {\n            unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n            debouncedFilter,\n            hasUnifiedData: !!unifiedData,\n            isArray: Array.isArray(unifiedData)\n        });\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data available, returning empty array\");\n            return [];\n        }\n        // Performance optimization: Check cache first (temporarily disabled for debugging)\n        // const dataHash = generateDataHash(unifiedData)\n        // const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash)\n        // if (filterCache.has(cacheKey)) {\n        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')\n        //     return filterCache.get(cacheKey)!\n        // }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting fresh filter calculation:\", {\n            totalItems: unifiedData.length,\n            filter: debouncedFilter\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                if (item.originalData) {\n                    var _item_originalData_trainer;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                        if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter - only apply if explicitly set by user\n            if (debouncedFilter.members && (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) || ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in))) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Applying member filter to item:\", {\n                    itemId: item.id,\n                    itemCategory: item.category,\n                    itemMembers: item.members,\n                    memberFilter: debouncedFilter.members\n                });\n                if (item.members && item.members.length > 0) {\n                    var _debouncedFilter_members_id2, _debouncedFilter_members_id3;\n                    const memberIds = item.members.map((member)=>member.id);\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item member IDs:\", memberIds);\n                    if (((_debouncedFilter_members_id2 = debouncedFilter.members.id) === null || _debouncedFilter_members_id2 === void 0 ? void 0 : _debouncedFilter_members_id2.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (eq):\", {\n                            itemId: item.id,\n                            expectedMemberId: debouncedFilter.members.id.eq,\n                            actualMemberIds: memberIds\n                        });\n                        return false;\n                    }\n                    if (((_debouncedFilter_members_id3 = debouncedFilter.members.id) === null || _debouncedFilter_members_id3 === void 0 ? void 0 : _debouncedFilter_members_id3.in) && debouncedFilter.members.id.in.length > 0) {\n                        const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                        if (!hasMatchingMember) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (in):\", {\n                                itemId: item.id,\n                                expectedMemberIds: debouncedFilter.members.id.in,\n                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map((id)=>\"ID:\".concat(id)),\n                                actualMemberIds: memberIds,\n                                actualMemberIdsDetailed: memberIds.map((id)=>\"ID:\".concat(id)),\n                                intersection: memberIds.filter((id)=>{\n                                    var _debouncedFilter_members_id_in, _debouncedFilter_members_id, _debouncedFilter_members;\n                                    return (_debouncedFilter_members = debouncedFilter.members) === null || _debouncedFilter_members === void 0 ? void 0 : (_debouncedFilter_members_id = _debouncedFilter_members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : (_debouncedFilter_members_id_in = _debouncedFilter_members_id.in) === null || _debouncedFilter_members_id_in === void 0 ? void 0 : _debouncedFilter_members_id_in.includes(id);\n                                })\n                            });\n                            return false;\n                        } else {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item passed member filter (in):\", {\n                                itemId: item.id,\n                                matchingMemberIds: debouncedFilter.members.id.in.filter((id)=>memberIds.includes(id))\n                            });\n                        }\n                    }\n                } else {\n                    // If item has no members but filter is set, filter it out\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item has no members, filtering out:\", {\n                        itemId: item.id,\n                        itemCategory: item.category,\n                        memberFilter: debouncedFilter.members\n                    });\n                    return false;\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter calculation complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key]),\n            sampleFilteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_members;\n                return {\n                    id: item.id,\n                    category: item.category,\n                    memberCount: ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.length) || 0\n                };\n            })\n        });\n        // Performance optimization: Cache the result (temporarily disabled for debugging)\n        // if (filterCache.size >= CACHE_SIZE_LIMIT) {\n        //     // Remove oldest entries when cache is full\n        //     const firstKey = filterCache.keys().next().value\n        //     if (firstKey) {\n        //         filterCache.delete(firstKey)\n        //     }\n        // }\n        // filterCache.set(cacheKey, filtered)\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    // Wrap setFilter to add debugging\n    const debugSetFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newFilter)=>{\n        var _stack;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] setFilter called:\", {\n            newFilter,\n            newFilterStringified: JSON.stringify(newFilter),\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            stackTrace: (_stack = new Error().stack) === null || _stack === void 0 ? void 0 : _stack.split(\"\\n\").slice(1, 8).join(\"\\n\")\n        });\n        setFilter(newFilter);\n    }, [\n        filter\n    ]);\n    return {\n        filter,\n        setFilter: debugSetFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});