"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Processing vessel filter:\", data);\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Vessel filter result:\", next.vesselID);\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Processing trainingType filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataType: typeof data,\n                dataValue: data\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Array trainingType filter set:\", {\n                    originalData: data,\n                    mappedValues,\n                    filterResult: next.trainingTypes\n                });\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Single trainingType filter set:\", {\n                    originalData: data,\n                    containsValue,\n                    filterResult: next.trainingTypes\n                });\n            } else {\n                delete next.trainingTypes;\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] TrainingType filter cleared\");\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainer = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainer = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data provided for filtering\");\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering unified data:\", {\n            totalRecords: unifiedData.length,\n            activeFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key] !== undefined),\n            filterValues: debouncedFilter,\n            cacheKey: cacheKey.substring(0, 100) + \"...\" // Truncate for logging\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                if (debouncedFilter.vesselID.eq && item.vesselID !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(item.vesselID)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _item_trainingType1, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1, _debouncedFilter_trainingTypes_id2, _debouncedFilter_trainingTypes_id3, _debouncedFilter_trainingTypes_id4, _debouncedFilter_trainingTypes_id5;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Training type filter check:\", {\n                    itemId: item.id,\n                    itemTrainingTypeName: ((_item_trainingType1 = item.trainingType) === null || _item_trainingType1 === void 0 ? void 0 : _item_trainingType1.title) || \"Unknown\",\n                    trainingTypeId,\n                    itemTrainingTypeID: item.trainingTypeID,\n                    itemTrainingType: item.trainingType,\n                    filterContains: (_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains,\n                    filterIn: (_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in,\n                    containsMatch: ((_debouncedFilter_trainingTypes_id2 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id2 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id2.contains) ? trainingTypeId === debouncedFilter.trainingTypes.id.contains : \"N/A\",\n                    inMatch: ((_debouncedFilter_trainingTypes_id3 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id3 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id3.in) ? debouncedFilter.trainingTypes.id.in.includes(trainingTypeId) : \"N/A\"\n                });\n                if (((_debouncedFilter_trainingTypes_id4 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id4 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id4.contains) && trainingTypeId !== debouncedFilter.trainingTypes.id.contains) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by contains filter:\", {\n                        itemId: item.id,\n                        trainingTypeId,\n                        expectedContains: debouncedFilter.trainingTypes.id.contains\n                    });\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id5 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id5 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id5.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeId)) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by in filter:\", {\n                        itemId: item.id,\n                        trainingTypeId,\n                        expectedIn: debouncedFilter.trainingTypes.id.in\n                    });\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer && item.originalData) {\n                var _item_originalData_trainer;\n                const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                if (trainerId) {\n                    var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                    if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerId !== debouncedFilter.trainer.id.eq) {\n                        return false;\n                    }\n                    if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerId)) {\n                        return false;\n                    }\n                } else if (debouncedFilter.trainer.id) {\n                    // If trainer filter is applied but no trainer data exists, exclude this item\n                    return false;\n                }\n            }\n            // Member filter\n            if (debouncedFilter.members && item.members) {\n                var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                    return false;\n                }\n                if ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in) {\n                    const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            categoryBreakdown: filtered.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});