"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing trainingType filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataValue: data === null || data === void 0 ? void 0 : data.value,\n                currentTrainingTypes: filter.trainingTypes\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (array):\", {\n                    mappedValues,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (single):\", {\n                    containsValue,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else {\n                delete next.trainingTypes;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Deleted trainingTypes filter\");\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainer = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainer = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Final filter state:\", {\n            type,\n            previousFilter: filter,\n            nextFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDE80 [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting filter operation:\", {\n            unifiedDataLength: unifiedData.length,\n            debouncedFilter,\n            hasTrainingTypeFilter: !!debouncedFilter.trainingTypes\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                if (debouncedFilter.vesselID.eq && item.vesselID !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(item.vesselID)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer && item.originalData) {\n                var _item_originalData_trainer;\n                const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                if (trainerId) {\n                    var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                    if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerId !== debouncedFilter.trainer.id.eq) {\n                        return false;\n                    }\n                    if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerId)) {\n                        return false;\n                    }\n                } else if (debouncedFilter.trainer.id) {\n                    // If trainer filter is applied but no trainer data exists, exclude this item\n                    return false;\n                }\n            }\n            // Member filter\n            if (debouncedFilter.members && item.members) {\n                var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                    return false;\n                }\n                if ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in) {\n                    const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});