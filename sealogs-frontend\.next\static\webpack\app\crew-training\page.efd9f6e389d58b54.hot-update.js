"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing trainingType filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataValue: data === null || data === void 0 ? void 0 : data.value,\n                currentTrainingTypes: filter.trainingTypes\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (array):\", {\n                    mappedValues,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (single):\", {\n                    containsValue,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else {\n                delete next.trainingTypes;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Deleted trainingTypes filter\");\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainer = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainer = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Final filter state:\", {\n            type,\n            previousFilter: filter,\n            nextFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDE80 [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting filter operation:\", {\n            unifiedDataLength: unifiedData.length,\n            debouncedFilter,\n            hasTrainingTypeFilter: !!debouncedFilter.trainingTypes\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                if (debouncedFilter.vesselID.eq && item.vesselID !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(item.vesselID)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _item_trainingType1, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1, _debouncedFilter_trainingTypes_id2, _debouncedFilter_trainingTypes_id3;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Training type filter check:\", {\n                    itemId: item.id,\n                    trainingTypeTitle: (_item_trainingType1 = item.trainingType) === null || _item_trainingType1 === void 0 ? void 0 : _item_trainingType1.title,\n                    trainingTypeId,\n                    trainingTypeIdNum,\n                    filterContains: (_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains,\n                    filterIn: (_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in,\n                    item: {\n                        trainingTypeID: item.trainingTypeID,\n                        trainingType: item.trainingType,\n                        category: item.category\n                    }\n                });\n                if (((_debouncedFilter_trainingTypes_id2 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id2 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id2.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by contains check:\", {\n                        itemId: item.id,\n                        trainingTypeIdNum,\n                        expectedContains: debouncedFilter.trainingTypes.id.contains\n                    });\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id3 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id3 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id3.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by in check:\", {\n                        itemId: item.id,\n                        trainingTypeIdNum,\n                        expectedIn: debouncedFilter.trainingTypes.id.in\n                    });\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer && item.originalData) {\n                var _item_originalData_trainer;\n                const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                if (trainerId) {\n                    var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                    if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerId !== debouncedFilter.trainer.id.eq) {\n                        return false;\n                    }\n                    if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerId)) {\n                        return false;\n                    }\n                } else if (debouncedFilter.trainer.id) {\n                    // If trainer filter is applied but no trainer data exists, exclude this item\n                    return false;\n                }\n            }\n            // Member filter\n            if (debouncedFilter.members && item.members) {\n                var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                    return false;\n                }\n                if ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in) {\n                    const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter operation completed:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: debouncedFilter,\n            filteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_trainingType;\n                return {\n                    id: item.id,\n                    trainingType: (_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title,\n                    category: item.category,\n                    trainingTypeID: item.trainingTypeID\n                };\n            })\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});