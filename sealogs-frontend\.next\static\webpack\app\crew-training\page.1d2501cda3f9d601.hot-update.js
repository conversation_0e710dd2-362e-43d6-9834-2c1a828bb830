"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Processing vessel filter:\", data);\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Vessel filter result:\", next.vesselID);\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Processing trainingType filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataType: typeof data,\n                dataValue: data\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Array trainingType filter set:\", {\n                    originalData: data,\n                    mappedValues,\n                    filterResult: next.trainingTypes\n                });\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Single trainingType filter set:\", {\n                    originalData: data,\n                    containsValue,\n                    filterResult: next.trainingTypes\n                });\n            } else {\n                delete next.trainingTypes;\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] TrainingType filter cleared\");\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                next.trainer = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.trainer = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Setting new filter:\", {\n            previousFilter: filter,\n            newFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data provided for filtering\");\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering unified data:\", {\n            totalRecords: unifiedData.length,\n            activeFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key] !== undefined),\n            filterValues: debouncedFilter,\n            cacheKey: cacheKey.substring(0, 100) + \"...\" // Truncate for logging\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                if (debouncedFilter.vesselID.eq && item.vesselID !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(item.vesselID)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _item_trainingType1, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1, _debouncedFilter_trainingTypes_id2, _debouncedFilter_trainingTypes_id3, _debouncedFilter_trainingTypes_id4, _debouncedFilter_trainingTypes_id5;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Training type filter check:\", {\n                    itemId: item.id,\n                    itemTrainingTypeName: ((_item_trainingType1 = item.trainingType) === null || _item_trainingType1 === void 0 ? void 0 : _item_trainingType1.title) || \"Unknown\",\n                    trainingTypeId,\n                    itemTrainingTypeID: item.trainingTypeID,\n                    itemTrainingType: item.trainingType,\n                    filterContains: (_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains,\n                    filterIn: (_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in,\n                    containsMatch: ((_debouncedFilter_trainingTypes_id2 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id2 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id2.contains) ? trainingTypeId === debouncedFilter.trainingTypes.id.contains : \"N/A\",\n                    inMatch: ((_debouncedFilter_trainingTypes_id3 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id3 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id3.in) ? debouncedFilter.trainingTypes.id.in.includes(trainingTypeId) : \"N/A\"\n                });\n                if (((_debouncedFilter_trainingTypes_id4 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id4 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id4.contains) && trainingTypeId !== debouncedFilter.trainingTypes.id.contains) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by contains filter:\", {\n                        itemId: item.id,\n                        trainingTypeId,\n                        expectedContains: debouncedFilter.trainingTypes.id.contains\n                    });\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id5 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id5 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id5.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeId)) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by in filter:\", {\n                        itemId: item.id,\n                        trainingTypeId,\n                        expectedIn: debouncedFilter.trainingTypes.id.in\n                    });\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer && item.originalData) {\n                var _item_originalData_trainer;\n                const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                if (trainerId) {\n                    var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                    if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerId !== debouncedFilter.trainer.id.eq) {\n                        return false;\n                    }\n                    if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerId)) {\n                        return false;\n                    }\n                } else if (debouncedFilter.trainer.id) {\n                    // If trainer filter is applied but no trainer data exists, exclude this item\n                    return false;\n                }\n            }\n            // Member filter\n            if (debouncedFilter.members && item.members) {\n                var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                    return false;\n                }\n                if ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in) {\n                    const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            categoryBreakdown: filtered.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});