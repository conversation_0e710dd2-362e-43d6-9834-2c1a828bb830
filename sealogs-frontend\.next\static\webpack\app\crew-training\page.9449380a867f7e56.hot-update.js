"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/utils/crew-training-utils.ts ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _lib_actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * Optimized version with better error handling and performance\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    try {\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n            // Enhanced vessel data transformation with position information\n            let completeVesselData = training.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n                try {\n                    // Get complete vessel data including position, icon, and other metadata\n                    completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                    // Ensure we preserve original vessel data if transformation fails\n                    if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                        completeVesselData = training.vessel;\n                    }\n                    // Add position information if available\n                    if (training.vessel.position && !completeVesselData.position) {\n                        completeVesselData.position = training.vessel.position;\n                    }\n                    // Add location type if available\n                    if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                        completeVesselData.trainingLocationType = training.trainingLocationType;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                    completeVesselData = training.vessel;\n                }\n            }\n            // Enhanced member deduplication and normalization\n            const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n            const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n                // Check if member already exists in the accumulator\n                const existingMember = acc.find((m)=>m.id === member.id);\n                if (existingMember) {\n                    // Update existing member with more complete data\n                    existingMember.firstName = member.firstName || existingMember.firstName;\n                    existingMember.surname = member.surname || existingMember.surname;\n                    existingMember.email = member.email || existingMember.email;\n                } else {\n                    // Add new member with normalized data\n                    acc.push({\n                        id: member.id,\n                        firstName: member.firstName || \"\",\n                        surname: member.surname || \"\",\n                        email: member.email || \"\",\n                        ...member // Preserve any additional member data\n                    });\n                }\n                return acc;\n            }, []);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n                vessel: completeVesselData,\n                trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: deduplicatedMembers,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                category: \"completed\",\n                originalData: training\n            };\n        });\n    } catch (error) {\n        console.error(\"Error transforming completed training data:\", error);\n        return [];\n    }\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        return [];\n    }\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item\n            if (!hasValidVesselMembers) {\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_lib_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            group.members.forEach((member)=>{\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                } else {\n                    console.warn(\"Invalid member:\", member);\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            try {\n                var _group_status, _group_status1;\n                // Determine category based on status\n                let category;\n                if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                    category = \"overdue\";\n                } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                    category = \"upcoming\";\n                } else {\n                    category = \"upcoming\" // Default for future due dates\n                    ;\n                }\n                // Enhanced vessel data with position information\n                // Create a new object to avoid \"object is not extensible\" errors\n                const enhancedVessel = {\n                    ...group.vessel || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    // Add training location type if available\n                    ...group.trainingLocationType && {\n                        trainingLocationType: group.trainingLocationType\n                    }\n                };\n                const result = {\n                    id: group.id,\n                    dueDate: group.dueDate,\n                    vesselID: group.vesselID,\n                    vessel: enhancedVessel,\n                    trainingTypeID: group.trainingTypeID,\n                    trainingType: group.trainingType || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    members: mergedMembers,\n                    status: group.status,\n                    category,\n                    originalData: group\n                };\n                return result;\n            } catch (error) {\n                console.error(\"Error creating unified record:\", error, \"for group:\", group);\n                return null // Return null to filter out failed records\n                ;\n            }\n        }).filter(Boolean)// Filter out null values and cast\n        ;\n        return mergedDues;\n    } catch (error) {\n        console.error(\"Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        return [];\n    }\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            // Handle both string and number IDs\n            const itemId = typeof item.id === \"string\" ? parseInt(item.id, 10) : item.id;\n            if (!item || !itemId && itemId !== 0 || isNaN(itemId)) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(itemId)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(itemId);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(itemId, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * Optimized version with better performance and error handling\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for sorting\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting sort:\", {\n        totalRecords: data.length,\n        categories: data.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    try {\n        return data.sort((a, b)=>{\n            try {\n                // First sort by priority (overdue > upcoming > completed)\n                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n                if (priorityDiff !== 0) {\n                    return priorityDiff;\n                }\n                // Within same priority, sort by date with error handling\n                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;\n                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;\n                // Handle invalid dates\n                if (isNaN(dateA) && isNaN(dateB)) return 0;\n                if (isNaN(dateA)) return 1;\n                if (isNaN(dateB)) return -1;\n                if (a.category === \"overdue\") {\n                    // For overdue: most overdue first (earliest due date first)\n                    return dateA - dateB;\n                } else if (a.category === \"upcoming\") {\n                    // For upcoming: soonest due date first\n                    return dateA - dateB;\n                } else {\n                    // For completed: most recent completion first (latest date first)\n                    return dateB - dateA;\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error comparing items in sort:\", error, {\n                    a,\n                    b\n                });\n                return 0 // Keep original order if comparison fails\n                ;\n            }\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in sorting:\", error);\n        return data // Return original data if sorting fails\n        ;\n    }\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Sort with priority-based ordering (deduplication removed)\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\n"));

/***/ })

});