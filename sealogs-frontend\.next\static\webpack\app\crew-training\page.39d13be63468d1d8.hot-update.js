"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/utils/crew-training-utils.ts ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _lib_actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * Optimized version with better error handling and performance\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    try {\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n            // Enhanced vessel data transformation with position information\n            let completeVesselData = training.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n                try {\n                    // Get complete vessel data including position, icon, and other metadata\n                    completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                    // Ensure we preserve original vessel data if transformation fails\n                    if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                        completeVesselData = training.vessel;\n                    }\n                    // Add position information if available\n                    if (training.vessel.position && !completeVesselData.position) {\n                        completeVesselData.position = training.vessel.position;\n                    }\n                    // Add location type if available\n                    if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                        completeVesselData.trainingLocationType = training.trainingLocationType;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                    completeVesselData = training.vessel;\n                }\n            }\n            // Enhanced member deduplication and normalization\n            const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n            const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n                // Check if member already exists in the accumulator\n                const existingMember = acc.find((m)=>m.id === member.id);\n                if (existingMember) {\n                    // Update existing member with more complete data\n                    existingMember.firstName = member.firstName || existingMember.firstName;\n                    existingMember.surname = member.surname || existingMember.surname;\n                    existingMember.email = member.email || existingMember.email;\n                } else {\n                    // Add new member with normalized data\n                    acc.push({\n                        id: member.id,\n                        firstName: member.firstName || \"\",\n                        surname: member.surname || \"\",\n                        email: member.email || \"\",\n                        ...member // Preserve any additional member data\n                    });\n                }\n                return acc;\n            }, []);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n                vessel: completeVesselData,\n                trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: deduplicatedMembers,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                category: \"completed\",\n                originalData: training\n            };\n        });\n    } catch (error) {\n        console.error(\"Error transforming completed training data:\", error);\n        return [];\n    }\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No training session dues provided\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Raw training session dues input:\", {\n        totalRecords: trainingSessionDues.length,\n        sampleRecord: trainingSessionDues[0]\n    });\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item but log it\n            if (!hasValidVesselMembers) {\n                var _item_vessel_seaLogsMembers_nodes1, _item_vessel_seaLogsMembers1, _item_vessel1;\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Item without vessel member match:\", {\n                    memberID: item.memberID,\n                    vesselID: item.vesselID,\n                    vesselMembers: (_item_vessel1 = item.vessel) === null || _item_vessel1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers1 = _item_vessel1.seaLogsMembers) === null || _item_vessel_seaLogsMembers1 === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes1 = _item_vessel_seaLogsMembers1.nodes) === null || _item_vessel_seaLogsMembers_nodes1 === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes1.map((m)=>m === null || m === void 0 ? void 0 : m.id)\n                });\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n            originalCount: trainingSessionDues.length,\n            filteredCount: filteredData.length,\n            removedCount: trainingSessionDues.length - filteredData.length\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_lib_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n            totalRecords: dueWithStatus.length,\n            statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n                const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n                acc[key] = (acc[key] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n            groupCount: Object.keys(groupedDues).length,\n            groupKeys: Object.keys(groupedDues),\n            sampleGroup: Object.values(groupedDues)[0]\n        });\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting member merging for groups:\", Object.values(groupedDues).length);\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_members;\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing group:\", {\n                id: group.id,\n                vesselID: group.vesselID,\n                trainingTypeID: group.trainingTypeID,\n                membersCount: ((_group_members = group.members) === null || _group_members === void 0 ? void 0 : _group_members.length) || 0,\n                members: group.members\n            });\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing members for group:\", group.members);\n            group.members.forEach((member)=>{\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing member:\", member);\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                } else {\n                    console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Invalid member:\", member);\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Merged members result:\", mergedMembers);\n            try {\n                var _group_status, _group_status1;\n                // Determine category based on status\n                let category;\n                if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                    category = \"overdue\";\n                } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                    category = \"upcoming\";\n                } else {\n                    category = \"upcoming\" // Default for future due dates\n                    ;\n                }\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Determined category:\", category, \"for status:\", group.status);\n                // Enhanced vessel data with position information\n                // Create a new object to avoid \"object is not extensible\" errors\n                const enhancedVessel = {\n                    ...group.vessel || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    // Add training location type if available\n                    ...group.trainingLocationType && {\n                        trainingLocationType: group.trainingLocationType\n                    }\n                };\n                const result = {\n                    id: group.id,\n                    dueDate: group.dueDate,\n                    vesselID: group.vesselID,\n                    vessel: enhancedVessel,\n                    trainingTypeID: group.trainingTypeID,\n                    trainingType: group.trainingType || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    members: mergedMembers,\n                    status: group.status,\n                    category,\n                    originalData: group\n                };\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Created unified record:\", result);\n                return result;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error creating unified record:\", error, \"for group:\", group);\n                return null // Return null to filter out failed records\n                ;\n            }\n        }).filter(Boolean)// Filter out null values and cast\n        ;\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0],\n            allRecords: mergedDues\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for deduplication\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting deduplication:\", {\n        totalRecords: data.length,\n        sampleRecord: data[0]\n    });\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            // Handle both string and number IDs\n            const itemId = typeof item.id === \"string\" ? parseInt(item.id, 10) : item.id;\n            if (!item || !itemId && itemId !== 0 || isNaN(itemId)) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(itemId)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(itemId);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(itemId, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Deduplication complete:\", {\n            originalCount: data.length,\n            deduplicatedCount: deduplicatedData.length,\n            removedDuplicates: data.length - deduplicatedData.length,\n            categoryBreakdown: deduplicatedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * Optimized version with better performance and error handling\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for sorting\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting sort:\", {\n        totalRecords: data.length,\n        categories: data.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    try {\n        return data.sort((a, b)=>{\n            try {\n                // First sort by priority (overdue > upcoming > completed)\n                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n                if (priorityDiff !== 0) {\n                    return priorityDiff;\n                }\n                // Within same priority, sort by date with error handling\n                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;\n                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;\n                // Handle invalid dates\n                if (isNaN(dateA) && isNaN(dateB)) return 0;\n                if (isNaN(dateA)) return 1;\n                if (isNaN(dateB)) return -1;\n                if (a.category === \"overdue\") {\n                    // For overdue: most overdue first (earliest due date first)\n                    return dateA - dateB;\n                } else if (a.category === \"upcoming\") {\n                    // For upcoming: soonest due date first\n                    return dateA - dateB;\n                } else {\n                    // For completed: most recent completion first (latest date first)\n                    return dateB - dateA;\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error comparing items in sort:\", error, {\n                    a,\n                    b\n                });\n                return 0 // Keep original order if comparison fails\n                ;\n            }\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in sorting:\", error);\n        return data // Return original data if sorting fails\n        ;\n    }\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Sort with priority-based ordering (deduplication removed)\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\n"));

/***/ })

});