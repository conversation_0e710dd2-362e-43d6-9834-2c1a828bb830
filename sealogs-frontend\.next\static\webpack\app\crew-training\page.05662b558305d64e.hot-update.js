"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing trainingType filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataValue: data === null || data === void 0 ? void 0 : data.value,\n                currentTrainingTypes: filter.trainingTypes\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (array):\", {\n                    mappedValues,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (single):\", {\n                    containsValue,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else {\n                delete next.trainingTypes;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Deleted trainingTypes filter\");\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing trainer filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataValue: data === null || data === void 0 ? void 0 : data.value,\n                currentTrainer: filter.trainer\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainer (array):\", {\n                    mappedValues,\n                    nextTrainer: next.trainer\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainer (single):\", {\n                    eqValue,\n                    nextTrainer: next.trainer\n                });\n            } else {\n                delete next.trainer;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Deleted trainer filter\");\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Final filter state:\", {\n            type,\n            previousFilter: filter,\n            nextFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDE80 [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting filter operation:\", {\n            unifiedDataLength: unifiedData.length,\n            debouncedFilter,\n            hasTrainingTypeFilter: !!debouncedFilter.trainingTypes\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _item_trainingType, _item_trainingType1;\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering item:\", {\n                itemId: item.id,\n                category: item.category,\n                trainingType: (_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title,\n                vesselID: item.vesselID,\n                debouncedFilter\n            });\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by category:\", {\n                        itemCategory: item.category,\n                        filterCategory: debouncedFilter.category\n                    });\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Vessel filter check:\", {\n                    itemVesselID: item.vesselID,\n                    itemVesselIdNum,\n                    filterVesselID: debouncedFilter.vesselID,\n                    itemVesselIDType: typeof item.vesselID\n                });\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by vessel eq:\", {\n                        itemVesselID: item.vesselID,\n                        itemVesselIdNum,\n                        expectedVesselID: debouncedFilter.vesselID.eq\n                    });\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by vessel in:\", {\n                        itemVesselID: item.vesselID,\n                        itemVesselIdNum,\n                        expectedVesselIDs: debouncedFilter.vesselID.in\n                    });\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType2, _item_trainingType3, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1, _debouncedFilter_trainingTypes_id2, _debouncedFilter_trainingTypes_id3;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType2 = item.trainingType) === null || _item_trainingType2 === void 0 ? void 0 : _item_trainingType2.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Training type filter check:\", {\n                    itemId: item.id,\n                    trainingTypeTitle: (_item_trainingType3 = item.trainingType) === null || _item_trainingType3 === void 0 ? void 0 : _item_trainingType3.title,\n                    trainingTypeId,\n                    trainingTypeIdNum,\n                    filterContains: (_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains,\n                    filterIn: (_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in,\n                    item: {\n                        trainingTypeID: item.trainingTypeID,\n                        trainingType: item.trainingType,\n                        category: item.category\n                    }\n                });\n                if (((_debouncedFilter_trainingTypes_id2 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id2 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id2.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by contains check:\", {\n                        itemId: item.id,\n                        trainingTypeIdNum,\n                        expectedContains: debouncedFilter.trainingTypes.id.contains\n                    });\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id3 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id3 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id3.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by in check:\", {\n                        itemId: item.id,\n                        trainingTypeIdNum,\n                        expectedIn: debouncedFilter.trainingTypes.id.in\n                    });\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Trainer filter check:\", {\n                    itemId: item.id,\n                    category: item.category,\n                    hasOriginalData: !!item.originalData,\n                    originalData: item.originalData,\n                    filterTrainer: debouncedFilter.trainer,\n                    itemType: item.category\n                });\n                if (item.originalData) {\n                    var _item_originalData_trainer, _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Trainer ID check:\", {\n                        itemId: item.id,\n                        trainerId,\n                        trainerIdNum,\n                        trainerIdType: typeof trainerId,\n                        filterEq: (_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq,\n                        filterIn: (_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in,\n                        originalDataKeys: Object.keys(item.originalData || {}),\n                        trainerData: item.originalData.trainer\n                    });\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer eq:\", {\n                                itemId: item.id,\n                                trainerIdNum,\n                                expectedTrainerID: debouncedFilter.trainer.id.eq\n                            });\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer in:\", {\n                                itemId: item.id,\n                                trainerIdNum,\n                                expectedTrainerIDs: debouncedFilter.trainer.id.in\n                            });\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // If trainer filter is applied but no trainer data exists, exclude this item\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out - no trainer data:\", {\n                            itemId: item.id,\n                            category: item.category,\n                            hasOriginalData: !!item.originalData,\n                            originalDataKeys: Object.keys(item.originalData || {})\n                        });\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] No originalData, checking other trainer fields:\", {\n                        itemId: item.id,\n                        category: item.category,\n                        itemKeys: Object.keys(item),\n                        hasTrainerField: \"trainer\" in itemAsAny,\n                        hasTrainerID: \"trainerID\" in itemAsAny,\n                        trainer: itemAsAny.trainer,\n                        trainerID: itemAsAny.trainerID\n                    });\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id4, _debouncedFilter_trainer_id5;\n                        if (((_debouncedFilter_trainer_id4 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id4 === void 0 ? void 0 : _debouncedFilter_trainer_id4.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer eq (no originalData):\", {\n                                itemId: item.id,\n                                itemTrainerIdNum,\n                                expectedTrainerID: debouncedFilter.trainer.id.eq\n                            });\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id5 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id5 === void 0 ? void 0 : _debouncedFilter_trainer_id5.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer in (no originalData):\", {\n                                itemId: item.id,\n                                itemTrainerIdNum,\n                                expectedTrainerIDs: debouncedFilter.trainer.id.in\n                            });\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // If trainer filter is applied but no trainer data exists anywhere, exclude this item\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out - no trainer data anywhere:\", {\n                            itemId: item.id,\n                            category: item.category\n                        });\n                        return false;\n                    }\n                }\n            }\n            // Member filter\n            if (debouncedFilter.members && item.members) {\n                var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                    return false;\n                }\n                if ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in) {\n                    const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item passed all filters:\", {\n                itemId: item.id,\n                category: item.category,\n                trainingType: (_item_trainingType1 = item.trainingType) === null || _item_trainingType1 === void 0 ? void 0 : _item_trainingType1.title\n            });\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter operation completed:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: debouncedFilter,\n            filteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_trainingType;\n                return {\n                    id: item.id,\n                    trainingType: (_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title,\n                    category: item.category,\n                    trainingTypeID: item.trainingTypeID\n                };\n            })\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});