"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    // Debug initial filter state\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Hook initialized:\", {\n        initialFilter,\n        actualInitialFilter: filter,\n        hasMembers: !!filter.members,\n        membersFilter: filter.members\n    });\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            dataLength: Array.isArray(data) ? data.length : \"N/A\",\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing member filter:\", {\n                data,\n                dataType: typeof data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                currentMembersFilter: filter.members\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.members = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (array):\", {\n                    mappedValues,\n                    newFilter: next.members\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.members = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (single):\", {\n                    eqValue,\n                    newFilter: next.members\n                });\n            } else {\n                delete next.members;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Cleared member filter\");\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter update complete:\", {\n            type,\n            previousFilter: filter,\n            newFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] filteredData calculation triggered:\", {\n            unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n            debouncedFilter,\n            hasUnifiedData: !!unifiedData,\n            isArray: Array.isArray(unifiedData)\n        });\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data available, returning empty array\");\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        // Temporarily disable cache to debug filter issues\n        // if (filterCache.has(cacheKey)) {\n        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')\n        //     return filterCache.get(cacheKey)!\n        // }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting fresh filter calculation:\", {\n            totalItems: unifiedData.length,\n            filter: debouncedFilter\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                if (item.originalData) {\n                    var _item_originalData_trainer;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                        if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter - only apply if explicitly set by user\n            if (debouncedFilter.members && (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) || ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in))) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Applying member filter to item:\", {\n                    itemId: item.id,\n                    itemCategory: item.category,\n                    itemMembers: item.members,\n                    memberFilter: debouncedFilter.members\n                });\n                if (item.members && item.members.length > 0) {\n                    var _debouncedFilter_members_id2, _debouncedFilter_members_id3;\n                    const memberIds = item.members.map((member)=>member.id);\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item member IDs:\", memberIds);\n                    if (((_debouncedFilter_members_id2 = debouncedFilter.members.id) === null || _debouncedFilter_members_id2 === void 0 ? void 0 : _debouncedFilter_members_id2.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (eq):\", {\n                            itemId: item.id,\n                            expectedMemberId: debouncedFilter.members.id.eq,\n                            actualMemberIds: memberIds\n                        });\n                        return false;\n                    }\n                    if (((_debouncedFilter_members_id3 = debouncedFilter.members.id) === null || _debouncedFilter_members_id3 === void 0 ? void 0 : _debouncedFilter_members_id3.in) && debouncedFilter.members.id.in.length > 0) {\n                        const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                        if (!hasMatchingMember) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (in):\", {\n                                itemId: item.id,\n                                expectedMemberIds: debouncedFilter.members.id.in,\n                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map((id)=>\"ID:\".concat(id)),\n                                actualMemberIds: memberIds,\n                                actualMemberIdsDetailed: memberIds.map((id)=>\"ID:\".concat(id)),\n                                intersection: memberIds.filter((id)=>{\n                                    var _debouncedFilter_members_id_in, _debouncedFilter_members_id, _debouncedFilter_members;\n                                    return (_debouncedFilter_members = debouncedFilter.members) === null || _debouncedFilter_members === void 0 ? void 0 : (_debouncedFilter_members_id = _debouncedFilter_members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : (_debouncedFilter_members_id_in = _debouncedFilter_members_id.in) === null || _debouncedFilter_members_id_in === void 0 ? void 0 : _debouncedFilter_members_id_in.includes(id);\n                                })\n                            });\n                            return false;\n                        } else {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item passed member filter (in):\", {\n                                itemId: item.id,\n                                matchingMemberIds: debouncedFilter.members.id.in.filter((id)=>memberIds.includes(id))\n                            });\n                        }\n                    }\n                } else {\n                    // If item has no members but filter is set, filter it out\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item has no members, filtering out:\", {\n                        itemId: item.id,\n                        itemCategory: item.category,\n                        memberFilter: debouncedFilter.members\n                    });\n                    return false;\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter calculation complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key]),\n            sampleFilteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_members;\n                return {\n                    id: item.id,\n                    category: item.category,\n                    memberCount: ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.length) || 0\n                };\n            })\n        });\n        // Performance optimization: Cache the result (temporarily disabled for debugging)\n        // if (filterCache.size >= CACHE_SIZE_LIMIT) {\n        //     // Remove oldest entries when cache is full\n        //     const firstKey = filterCache.keys().next().value\n        //     if (firstKey) {\n        //         filterCache.delete(firstKey)\n        //     }\n        // }\n        // filterCache.set(cacheKey, filtered)\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});