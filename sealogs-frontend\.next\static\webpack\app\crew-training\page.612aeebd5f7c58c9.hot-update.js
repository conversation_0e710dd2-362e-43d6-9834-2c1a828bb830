"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    // Debug initial filter state with detailed information\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Hook initialized with detailed state:\", {\n        initialFilter,\n        initialFilterStringified: JSON.stringify(initialFilter),\n        actualInitialFilter: filter,\n        actualFilterStringified: JSON.stringify(filter),\n        hasMembers: !!filter.members,\n        membersFilter: filter.members,\n        membersFilterStringified: JSON.stringify(filter.members),\n        initialFilterHasMembers: !!initialFilter.members,\n        initialFilterMembersFilter: initialFilter.members\n    });\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    // Debug debounced filter initialization\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounced filter initialized:\", {\n        debouncedFilter,\n        debouncedFilterStringified: JSON.stringify(debouncedFilter),\n        hasDebouncedMembers: !!debouncedFilter.members,\n        debouncedMembersFilter: debouncedFilter.members\n    });\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter change detected, setting up debounce:\", {\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            hasMembers: !!filter.members,\n            membersFilter: filter.members\n        });\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounce timeout triggered, updating debouncedFilter:\", {\n                newDebouncedFilter: filter,\n                newDebouncedFilterStringified: JSON.stringify(filter),\n                previousDebouncedFilter: debouncedFilter,\n                previousDebouncedFilterStringified: JSON.stringify(debouncedFilter)\n            });\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter,\n        debouncedFilter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            dataLength: Array.isArray(data) ? data.length : \"N/A\",\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing member filter:\", {\n                data,\n                dataType: typeof data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                currentMembersFilter: filter.members\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.members = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (array):\", {\n                    mappedValues,\n                    newFilter: next.members\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.members = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (single):\", {\n                    eqValue,\n                    newFilter: next.members\n                });\n            } else {\n                delete next.members;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Cleared member filter\");\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter update complete:\", {\n            type,\n            previousFilter: filter,\n            newFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] filteredData calculation triggered:\", {\n            unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n            debouncedFilter,\n            hasUnifiedData: !!unifiedData,\n            isArray: Array.isArray(unifiedData)\n        });\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data available, returning empty array\");\n            return [];\n        }\n        // Performance optimization: Check cache first (temporarily disabled for debugging)\n        // const dataHash = generateDataHash(unifiedData)\n        // const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash)\n        // if (filterCache.has(cacheKey)) {\n        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')\n        //     return filterCache.get(cacheKey)!\n        // }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting fresh filter calculation:\", {\n            totalItems: unifiedData.length,\n            filter: debouncedFilter\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                if (item.originalData) {\n                    var _item_originalData_trainer;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                        if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter - only apply if explicitly set by user\n            if (debouncedFilter.members && (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) || ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in))) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Applying member filter to item:\", {\n                    itemId: item.id,\n                    itemCategory: item.category,\n                    itemMembers: item.members,\n                    memberFilter: debouncedFilter.members\n                });\n                if (item.members && item.members.length > 0) {\n                    var _debouncedFilter_members_id2, _debouncedFilter_members_id3;\n                    const memberIds = item.members.map((member)=>member.id);\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item member IDs:\", memberIds);\n                    if (((_debouncedFilter_members_id2 = debouncedFilter.members.id) === null || _debouncedFilter_members_id2 === void 0 ? void 0 : _debouncedFilter_members_id2.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (eq):\", {\n                            itemId: item.id,\n                            expectedMemberId: debouncedFilter.members.id.eq,\n                            actualMemberIds: memberIds\n                        });\n                        return false;\n                    }\n                    if (((_debouncedFilter_members_id3 = debouncedFilter.members.id) === null || _debouncedFilter_members_id3 === void 0 ? void 0 : _debouncedFilter_members_id3.in) && debouncedFilter.members.id.in.length > 0) {\n                        const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                        if (!hasMatchingMember) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (in):\", {\n                                itemId: item.id,\n                                expectedMemberIds: debouncedFilter.members.id.in,\n                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map((id)=>\"ID:\".concat(id)),\n                                actualMemberIds: memberIds,\n                                actualMemberIdsDetailed: memberIds.map((id)=>\"ID:\".concat(id)),\n                                intersection: memberIds.filter((id)=>{\n                                    var _debouncedFilter_members_id_in, _debouncedFilter_members_id, _debouncedFilter_members;\n                                    return (_debouncedFilter_members = debouncedFilter.members) === null || _debouncedFilter_members === void 0 ? void 0 : (_debouncedFilter_members_id = _debouncedFilter_members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : (_debouncedFilter_members_id_in = _debouncedFilter_members_id.in) === null || _debouncedFilter_members_id_in === void 0 ? void 0 : _debouncedFilter_members_id_in.includes(id);\n                                })\n                            });\n                            return false;\n                        } else {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item passed member filter (in):\", {\n                                itemId: item.id,\n                                matchingMemberIds: debouncedFilter.members.id.in.filter((id)=>memberIds.includes(id))\n                            });\n                        }\n                    }\n                } else {\n                    // If item has no members but filter is set, filter it out\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item has no members, filtering out:\", {\n                        itemId: item.id,\n                        itemCategory: item.category,\n                        memberFilter: debouncedFilter.members\n                    });\n                    return false;\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter calculation complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key]),\n            sampleFilteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_members;\n                return {\n                    id: item.id,\n                    category: item.category,\n                    memberCount: ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.length) || 0\n                };\n            })\n        });\n        // Performance optimization: Cache the result (temporarily disabled for debugging)\n        // if (filterCache.size >= CACHE_SIZE_LIMIT) {\n        //     // Remove oldest entries when cache is full\n        //     const firstKey = filterCache.keys().next().value\n        //     if (firstKey) {\n        //         filterCache.delete(firstKey)\n        //     }\n        // }\n        // filterCache.set(cacheKey, filtered)\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});