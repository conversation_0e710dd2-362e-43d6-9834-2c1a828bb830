"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx":
/*!**************************************************************************!*\
  !*** ./src/components/filter/components/crew-dropdown/crew-dropdown.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CrewDropdown = (param)=>{\n    let { label = \"Trainer\", value, onChange, controlClasses = \"default\", placeholder = \"Trainer\", isClearable = false, filterByTrainingSessionMemberId = 0, trainerIdOptions = [], memberIdOptions = [], multi = false, offline = false, vesselID = 0, disabled = false } = param;\n    _s();\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedValue, setSelectedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [allCrewList, setAllCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [imCrew, setImCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // if this is a crew member or not\n    ;\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const [allFetchedData, setAllFetchedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentFilter, setCurrentFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentOffset, setCurrentOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const processCompleteData = (completeData)=>{\n        // If vesselID > 0, filter the crew list to display only the vessel crew.\n        const vesselCrewList = vesselID > 0 ? completeData.filter((crew)=>crew.vehicles.nodes.some((vehicle)=>+vehicle.id === vesselID)) : completeData;\n        if (vesselCrewList) {\n            if (imCrew && pathname === \"/reporting\") {\n                // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user\n                const userId = localStorage.getItem(\"userId\");\n                const filteredCrewList = vesselCrewList.filter((crew)=>{\n                    return crew.id === userId;\n                });\n                setCrewList(filteredCrewList);\n            } else {\n                setCrewList(vesselCrewList);\n            }\n            setAllCrewList(vesselCrewList);\n        }\n    };\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_6__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            const pageInfo = response.readSeaLogsMembers.pageInfo;\n            // Accumulate data from all pages\n            const newCompleteData = [\n                ...allFetchedData,\n                ...data\n            ];\n            setAllFetchedData(newCompleteData);\n            // If there are more pages, fetch the next page\n            if (pageInfo.hasNextPage) {\n                const newOffset = currentOffset + data.length;\n                setCurrentOffset(newOffset);\n                querySeaLogsMembersList({\n                    variables: {\n                        filter: currentFilter,\n                        offset: newOffset,\n                        limit: 100\n                    }\n                });\n                return; // Don't process the crew list yet, wait for all data\n            }\n            // All data has been fetched, now process the complete dataset\n            processCompleteData(newCompleteData);\n        },\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    const loadCrewMembers = async ()=>{\n        let filter = {\n            isArchived: {\n                eq: false\n            }\n        };\n        if (filterByTrainingSessionMemberId > 0) {\n            filter = {\n                ...filter,\n                trainingSessions: {\n                    members: {\n                        id: {\n                            contains: filterByTrainingSessionMemberId\n                        }\n                    }\n                }\n            };\n        }\n        if (offline) {\n            // querySeaLogsMembersList\n            const allCrews = await seaLogsMemberModel.getAll();\n            const data = allCrews.filter((crew)=>{\n                if (filterByTrainingSessionMemberId > 0) {\n                    return crew.isArchived === false && crew.trainingSessions.nodes.some((trainingSession)=>trainingSession.members.nodes.some((member)=>member.id === filterByTrainingSessionMemberId));\n                } else {\n                    return crew.isArchived === false;\n                }\n            });\n            if (data) {\n                if (imCrew && pathname === \"/reporting\") {\n                    // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user\n                    const userId = localStorage.getItem(\"userId\");\n                    const filteredCrewList = data.filter((crew)=>{\n                        return crew.id === userId;\n                    });\n                    setCrewList(filteredCrewList);\n                } else {\n                    setCrewList(data);\n                }\n                setAllCrewList(data);\n            }\n        } else {\n            // Reset accumulated data and set current filter for pagination\n            setAllFetchedData([]);\n            setCurrentOffset(0);\n            setCurrentFilter(filter);\n            await querySeaLogsMembersList({\n                variables: {\n                    filter: filter,\n                    offset: 0,\n                    limit: 100\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            setImCrew((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.isCrew)() || false);\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (value && crewList) {\n            const crew = crewList.find((crew)=>crew.id === value);\n            if (crew) {\n                const newSelectedValue = {\n                    value: crew.id,\n                    label: \"\".concat(crew.firstName || \"\", \" \").concat(crew.surname || \"\"),\n                    profile: {\n                        firstName: crew.firstName,\n                        surname: crew.surname,\n                        avatar: null\n                    }\n                };\n                setSelectedValue(newSelectedValue);\n            }\n        } else if (!value) {\n            // Reset to null when no value is provided to show placeholder\n            setSelectedValue(null);\n        }\n    }, [\n        value,\n        crewList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (trainerIdOptions.length > 0 && allCrewList.length > 0) {\n            const filteredCrewList = allCrewList.filter((crew)=>{\n                return trainerIdOptions.includes(crew.id);\n            });\n            setCrewList(filteredCrewList);\n        }\n    }, [\n        trainerIdOptions,\n        allCrewList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (memberIdOptions.length > 0 && allCrewList.length > 0) {\n            const filteredCrewList = allCrewList.filter((crew)=>{\n                return memberIdOptions.includes(crew.id);\n            });\n            setCrewList(filteredCrewList);\n        }\n    }, [\n        memberIdOptions,\n        allCrewList\n    ]);\n    // Debug logging for trainer filter\n    console.log(\"\\uD83C\\uDFAF [CrewDropdown] Render state:\", {\n        placeholder,\n        label,\n        multi,\n        crewListLength: (crewList === null || crewList === void 0 ? void 0 : crewList.length) || 0,\n        selectedValue,\n        trainerIdOptions,\n        memberIdOptions,\n        filterByTrainingSessionMemberId,\n        isTrainerFilter: placeholder === \"Trainer\",\n        isMemberFilter: placeholder === \"Crew\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_5__.Combobox, {\n        options: crewList === null || crewList === void 0 ? void 0 : crewList.map((crew)=>({\n                value: crew.id,\n                label: \"\".concat(crew.firstName || \"\", \" \").concat(crew.surname || \"\"),\n                profile: {\n                    firstName: crew.firstName,\n                    surname: crew.surname,\n                    avatar: null\n                }\n            })),\n        value: selectedValue,\n        onChange: (selectedOption)=>{\n            // Debug logging for trainer filter changes\n            console.log(\"\\uD83C\\uDFAF [CrewDropdown] onChange triggered:\", {\n                placeholder,\n                selectedOption,\n                isTrainerFilter: placeholder === \"Trainer\",\n                isMemberFilter: placeholder === \"Crew\",\n                multi,\n                previousSelectedValue: selectedValue\n            });\n            // selectedOption is the Option object from Combobox\n            setSelectedValue(selectedOption);\n            // Debug the data being passed to parent\n            const dataToPass = multi ? selectedOption : (selectedOption === null || selectedOption === void 0 ? void 0 : selectedOption.value) || null;\n            console.log(\"\\uD83C\\uDFAF [CrewDropdown] Calling parent onChange with:\", {\n                placeholder,\n                dataToPass,\n                isArray: Array.isArray(dataToPass),\n                dataType: typeof dataToPass\n            });\n            // Pass the crew ID to the parent component\n            onChange(dataToPass);\n        },\n        //label={label}\n        multi: multi,\n        //labelClassName=\"w-full\"\n        isLoading: !crewList,\n        placeholder: placeholder,\n        disabled: disabled\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\crew-dropdown\\\\crew-dropdown.tsx\",\n        lineNumber: 212,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewDropdown, \"QzdoccneQuSIS+l6dwgHW9/oQxM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = CrewDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewDropdown);\nvar _c;\n$RefreshReg$(_c, \"CrewDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\n"));

/***/ })

});