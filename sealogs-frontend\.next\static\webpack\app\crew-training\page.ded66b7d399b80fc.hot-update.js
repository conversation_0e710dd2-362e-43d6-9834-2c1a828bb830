"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] useState initializer called:\", {\n            initialFilter,\n            initialFilterStringified: JSON.stringify(initialFilter),\n            willUseEmptyObject: true\n        });\n        return {};\n    });\n    // Debug initial filter state with detailed information\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Hook initialized with detailed state:\", {\n        initialFilter,\n        initialFilterStringified: JSON.stringify(initialFilter),\n        actualInitialFilter: filter,\n        actualFilterStringified: JSON.stringify(filter),\n        hasMembers: !!filter.members,\n        membersFilter: filter.members,\n        membersFilterStringified: JSON.stringify(filter.members),\n        initialFilterHasMembers: !!initialFilter.members,\n        initialFilterMembersFilter: initialFilter.members\n    });\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] debouncedFilter useState initializer called:\", {\n            initialFilter,\n            initialFilterStringified: JSON.stringify(initialFilter),\n            willUseInitialFilter: true\n        });\n        return initialFilter;\n    });\n    // Debug debounced filter initialization\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounced filter initialized:\", {\n        debouncedFilter,\n        debouncedFilterStringified: JSON.stringify(debouncedFilter),\n        hasDebouncedMembers: !!debouncedFilter.members,\n        debouncedMembersFilter: debouncedFilter.members\n    });\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter change detected, setting up debounce:\", {\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            hasMembers: !!filter.members,\n            membersFilter: filter.members\n        });\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounce timeout triggered, updating debouncedFilter:\", {\n                newDebouncedFilter: filter,\n                newDebouncedFilterStringified: JSON.stringify(filter),\n                previousDebouncedFilter: debouncedFilter,\n                previousDebouncedFilterStringified: JSON.stringify(debouncedFilter)\n            });\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter,\n        debouncedFilter\n    ]);\n    // Add useEffect to track filter state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _stack;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter state changed:\", {\n            filter,\n            filterStringified: JSON.stringify(filter),\n            hasMembers: !!filter.members,\n            membersFilter: filter.members,\n            membersFilterStringified: JSON.stringify(filter.members),\n            stackTrace: (_stack = new Error().stack) === null || _stack === void 0 ? void 0 : _stack.split(\"\\n\").slice(1, 8).join(\"\\n\")\n        });\n    }, [\n        filter\n    ]);\n    // Add useEffect to track debouncedFilter state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] DebouncedFilter state changed:\", {\n            debouncedFilter,\n            debouncedFilterStringified: JSON.stringify(debouncedFilter),\n            hasDebouncedMembers: !!debouncedFilter.members,\n            debouncedMembersFilter: debouncedFilter.members,\n            debouncedMembersFilterStringified: JSON.stringify(debouncedFilter.members)\n        });\n    }, [\n        debouncedFilter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            dataLength: Array.isArray(data) ? data.length : \"N/A\",\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            isCrewFilter: type === \"member\"\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing member filter:\", {\n                data,\n                dataType: typeof data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                currentMembersFilter: filter.members\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.members = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (array):\", {\n                    mappedValues,\n                    newFilter: next.members\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.members = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (single):\", {\n                    eqValue,\n                    newFilter: next.members\n                });\n            } else {\n                delete next.members;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Cleared member filter\");\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter update complete:\", {\n            type,\n            previousFilter: filter,\n            newFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] filteredData calculation triggered:\", {\n            unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n            debouncedFilter,\n            hasUnifiedData: !!unifiedData,\n            isArray: Array.isArray(unifiedData)\n        });\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data available, returning empty array\");\n            return [];\n        }\n        // Performance optimization: Check cache first (temporarily disabled for debugging)\n        // const dataHash = generateDataHash(unifiedData)\n        // const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash)\n        // if (filterCache.has(cacheKey)) {\n        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')\n        //     return filterCache.get(cacheKey)!\n        // }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting fresh filter calculation:\", {\n            totalItems: unifiedData.length,\n            filter: debouncedFilter\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                if (item.originalData) {\n                    var _item_originalData_trainer;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                        if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter - only apply if explicitly set by user\n            if (debouncedFilter.members && (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) || ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in))) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Applying member filter to item:\", {\n                    itemId: item.id,\n                    itemCategory: item.category,\n                    itemMembers: item.members,\n                    memberFilter: debouncedFilter.members\n                });\n                if (item.members && item.members.length > 0) {\n                    var _debouncedFilter_members_id2, _debouncedFilter_members_id3;\n                    // Convert member IDs to numbers for consistent comparison\n                    const memberIds = item.members.map((member)=>{\n                        const id = member.id;\n                        return typeof id === \"string\" ? parseInt(id, 10) : id;\n                    });\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item member IDs (converted to numbers):\", memberIds);\n                    if (((_debouncedFilter_members_id2 = debouncedFilter.members.id) === null || _debouncedFilter_members_id2 === void 0 ? void 0 : _debouncedFilter_members_id2.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (eq):\", {\n                            itemId: item.id,\n                            expectedMemberId: debouncedFilter.members.id.eq,\n                            actualMemberIds: memberIds\n                        });\n                        return false;\n                    }\n                    if (((_debouncedFilter_members_id3 = debouncedFilter.members.id) === null || _debouncedFilter_members_id3 === void 0 ? void 0 : _debouncedFilter_members_id3.in) && debouncedFilter.members.id.in.length > 0) {\n                        const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                        if (!hasMatchingMember) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (in):\", {\n                                itemId: item.id,\n                                expectedMemberIds: debouncedFilter.members.id.in,\n                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map((id)=>\"ID:\".concat(id)),\n                                actualMemberIds: memberIds,\n                                actualMemberIdsDetailed: memberIds.map((id)=>\"ID:\".concat(id)),\n                                intersection: memberIds.filter((id)=>{\n                                    var _debouncedFilter_members_id_in, _debouncedFilter_members_id, _debouncedFilter_members;\n                                    return (_debouncedFilter_members = debouncedFilter.members) === null || _debouncedFilter_members === void 0 ? void 0 : (_debouncedFilter_members_id = _debouncedFilter_members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : (_debouncedFilter_members_id_in = _debouncedFilter_members_id.in) === null || _debouncedFilter_members_id_in === void 0 ? void 0 : _debouncedFilter_members_id_in.includes(id);\n                                }),\n                                dataTypeComparison: {\n                                    expectedTypes: debouncedFilter.members.id.in.map((id)=>typeof id),\n                                    actualTypes: memberIds.map((id)=>typeof id)\n                                }\n                            });\n                            return false;\n                        } else {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item passed member filter (in):\", {\n                                itemId: item.id,\n                                matchingMemberIds: debouncedFilter.members.id.in.filter((id)=>memberIds.includes(id))\n                            });\n                        }\n                    }\n                } else {\n                    // If item has no members but filter is set, filter it out\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item has no members, filtering out:\", {\n                        itemId: item.id,\n                        itemCategory: item.category,\n                        memberFilter: debouncedFilter.members\n                    });\n                    return false;\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter calculation complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key]),\n            sampleFilteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_members;\n                return {\n                    id: item.id,\n                    category: item.category,\n                    memberCount: ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.length) || 0\n                };\n            })\n        });\n        // Performance optimization: Cache the result (temporarily disabled for debugging)\n        // if (filterCache.size >= CACHE_SIZE_LIMIT) {\n        //     // Remove oldest entries when cache is full\n        //     const firstKey = filterCache.keys().next().value\n        //     if (firstKey) {\n        //         filterCache.delete(firstKey)\n        //     }\n        // }\n        // filterCache.set(cacheKey, filtered)\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    // Wrap setFilter to add debugging\n    const debugSetFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newFilter)=>{\n        var _stack;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] setFilter called:\", {\n            newFilter,\n            newFilterStringified: JSON.stringify(newFilter),\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            stackTrace: (_stack = new Error().stack) === null || _stack === void 0 ? void 0 : _stack.split(\"\\n\").slice(1, 8).join(\"\\n\")\n        });\n        setFilter(newFilter);\n    }, [\n        filter\n    ]);\n    return {\n        filter,\n        setFilter: debugSetFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});