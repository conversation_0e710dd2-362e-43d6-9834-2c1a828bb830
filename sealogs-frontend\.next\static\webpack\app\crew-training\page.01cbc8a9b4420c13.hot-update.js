"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            dataLength: Array.isArray(data) ? data.length : \"N/A\",\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing member filter:\", {\n                data,\n                dataType: typeof data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                currentMembersFilter: filter.members\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.members = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (array):\", {\n                    mappedValues,\n                    newFilter: next.members\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.members = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (single):\", {\n                    eqValue,\n                    newFilter: next.members\n                });\n            } else {\n                delete next.members;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Cleared member filter\");\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter update complete:\", {\n            type,\n            previousFilter: filter,\n            newFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] filteredData calculation triggered:\", {\n            unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n            debouncedFilter,\n            hasUnifiedData: !!unifiedData,\n            isArray: Array.isArray(unifiedData)\n        });\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data available, returning empty array\");\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting fresh filter calculation:\", {\n            totalItems: unifiedData.length,\n            filter: debouncedFilter\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                if (item.originalData) {\n                    var _item_originalData_trainer;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                        if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter\n            if (debouncedFilter.members) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Applying member filter to item:\", {\n                    itemId: item.id,\n                    itemCategory: item.category,\n                    itemMembers: item.members,\n                    memberFilter: debouncedFilter.members\n                });\n                if (item.members) {\n                    var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n                    const memberIds = item.members.map((member)=>member.id);\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item member IDs:\", memberIds);\n                    if (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (eq):\", {\n                            itemId: item.id,\n                            expectedMemberId: debouncedFilter.members.id.eq,\n                            actualMemberIds: memberIds\n                        });\n                        return false;\n                    }\n                    if ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in) {\n                        const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                        if (!hasMatchingMember) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (in):\", {\n                                itemId: item.id,\n                                expectedMemberIds: debouncedFilter.members.id.in,\n                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map((id)=>\"ID:\".concat(id)),\n                                actualMemberIds: memberIds,\n                                actualMemberIdsDetailed: memberIds.map((id)=>\"ID:\".concat(id)),\n                                intersection: memberIds.filter((id)=>{\n                                    var _debouncedFilter_members_id_in;\n                                    return (_debouncedFilter_members_id_in = debouncedFilter.members.id.in) === null || _debouncedFilter_members_id_in === void 0 ? void 0 : _debouncedFilter_members_id_in.includes(id);\n                                })\n                            });\n                            return false;\n                        } else {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item passed member filter (in):\", {\n                                itemId: item.id,\n                                matchingMemberIds: debouncedFilter.members.id.in.filter((id)=>memberIds.includes(id))\n                            });\n                        }\n                    }\n                } else {\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item has no members, filtering out:\", {\n                        itemId: item.id,\n                        itemCategory: item.category,\n                        memberFilter: debouncedFilter.members\n                    });\n                    return false;\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter calculation complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key]),\n            sampleFilteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_members;\n                return {\n                    id: item.id,\n                    category: item.category,\n                    memberCount: ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.length) || 0\n                };\n            })\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});