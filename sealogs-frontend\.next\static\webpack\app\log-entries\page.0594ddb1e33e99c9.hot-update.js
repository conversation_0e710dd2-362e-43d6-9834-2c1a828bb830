"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx":
/*!**************************************************************************!*\
  !*** ./src/components/filter/components/crew-dropdown/crew-dropdown.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/offline/models/seaLogsMember */ \"(app-pages-browser)/./src/app/offline/models/seaLogsMember.js\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _queries__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./queries */ \"(app-pages-browser)/./src/components/filter/components/crew-dropdown/queries.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CrewDropdown = (param)=>{\n    let { label = \"Trainer\", value, onChange, controlClasses = \"default\", placeholder = \"Trainer\", isClearable = false, filterByTrainingSessionMemberId = 0, trainerIdOptions = [], memberIdOptions = [], multi = false, offline = false, vesselID = 0, disabled = false } = param;\n    _s();\n    const [crewList, setCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [selectedValue, setSelectedValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(multi ? [] : null);\n    // Debug selectedValue changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDFAF [CrewDropdown] selectedValue changed:\", {\n            placeholder,\n            selectedValue,\n            selectedValueType: typeof selectedValue,\n            isArray: Array.isArray(selectedValue),\n            length: Array.isArray(selectedValue) ? selectedValue.length : \"N/A\"\n        });\n    }, [\n        selectedValue,\n        placeholder\n    ]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [allCrewList, setAllCrewList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const [imCrew, setImCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false) // if this is a crew member or not\n    ;\n    const seaLogsMemberModel = new _app_offline_models_seaLogsMember__WEBPACK_IMPORTED_MODULE_4__[\"default\"]();\n    const [allFetchedData, setAllFetchedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentFilter, setCurrentFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [currentOffset, setCurrentOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const processCompleteData = (completeData)=>{\n        // If vesselID > 0, filter the crew list to display only the vessel crew.\n        const vesselCrewList = vesselID > 0 ? completeData.filter((crew)=>crew.vehicles.nodes.some((vehicle)=>+vehicle.id === vesselID)) : completeData;\n        if (vesselCrewList) {\n            if (imCrew && pathname === \"/reporting\") {\n                // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user\n                const userId = localStorage.getItem(\"userId\");\n                const filteredCrewList = vesselCrewList.filter((crew)=>{\n                    return crew.id === userId;\n                });\n                setCrewList(filteredCrewList);\n            } else {\n                setCrewList(vesselCrewList);\n            }\n            setAllCrewList(vesselCrewList);\n        }\n    };\n    const [querySeaLogsMembersList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery)(_queries__WEBPACK_IMPORTED_MODULE_6__.ReadSeaLogsMembers, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readSeaLogsMembers.nodes;\n            const pageInfo = response.readSeaLogsMembers.pageInfo;\n            // Accumulate data from all pages\n            const newCompleteData = [\n                ...allFetchedData,\n                ...data\n            ];\n            setAllFetchedData(newCompleteData);\n            // If there are more pages, fetch the next page\n            if (pageInfo.hasNextPage) {\n                const newOffset = currentOffset + data.length;\n                setCurrentOffset(newOffset);\n                querySeaLogsMembersList({\n                    variables: {\n                        filter: currentFilter,\n                        offset: newOffset,\n                        limit: 100\n                    }\n                });\n                return; // Don't process the crew list yet, wait for all data\n            }\n            // All data has been fetched, now process the complete dataset\n            processCompleteData(newCompleteData);\n        },\n        onError: (error)=>{\n            console.error(\"querySeaLogsMembersList error\", error);\n        }\n    });\n    const loadCrewMembers = async ()=>{\n        let filter = {\n            isArchived: {\n                eq: false\n            }\n        };\n        if (filterByTrainingSessionMemberId > 0) {\n            filter = {\n                ...filter,\n                trainingSessions: {\n                    members: {\n                        id: {\n                            contains: filterByTrainingSessionMemberId\n                        }\n                    }\n                }\n            };\n        }\n        if (offline) {\n            // querySeaLogsMembersList\n            const allCrews = await seaLogsMemberModel.getAll();\n            const data = allCrews.filter((crew)=>{\n                if (filterByTrainingSessionMemberId > 0) {\n                    return crew.isArchived === false && crew.trainingSessions.nodes.some((trainingSession)=>trainingSession.members.nodes.some((member)=>member.id === filterByTrainingSessionMemberId));\n                } else {\n                    return crew.isArchived === false;\n                }\n            });\n            if (data) {\n                if (imCrew && pathname === \"/reporting\") {\n                    // A crew can create a Sea Time report for themselves only. Limit the dropdown to only the current user\n                    const userId = localStorage.getItem(\"userId\");\n                    const filteredCrewList = data.filter((crew)=>{\n                        return crew.id === userId;\n                    });\n                    setCrewList(filteredCrewList);\n                } else {\n                    setCrewList(data);\n                }\n                setAllCrewList(data);\n            }\n        } else {\n            // Reset accumulated data and set current filter for pagination\n            setAllFetchedData([]);\n            setCurrentOffset(0);\n            setCurrentFilter(filter);\n            await querySeaLogsMembersList({\n                variables: {\n                    filter: filter,\n                    offset: 0,\n                    limit: 100\n                }\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoading) {\n            setImCrew((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_3__.isCrew)() || false);\n            loadCrewMembers();\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        console.log(\"\\uD83C\\uDFAF [CrewDropdown] Value useEffect triggered:\", {\n            placeholder,\n            value,\n            valueType: typeof value,\n            crewListLength: (crewList === null || crewList === void 0 ? void 0 : crewList.length) || 0,\n            hasCrewList: !!crewList\n        });\n        if (value && crewList) {\n            const crew = crewList.find((crew)=>crew.id === value);\n            if (crew) {\n                const newSelectedValue = {\n                    value: crew.id,\n                    label: \"\".concat(crew.firstName || \"\", \" \").concat(crew.surname || \"\"),\n                    profile: {\n                        firstName: crew.firstName,\n                        surname: crew.surname,\n                        avatar: null\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [CrewDropdown] Setting selectedValue from value prop:\", {\n                    placeholder,\n                    value,\n                    crew,\n                    newSelectedValue\n                });\n                setSelectedValue(newSelectedValue);\n            }\n        } else if (!value) {\n            // Reset to null when no value is provided to show placeholder\n            console.log(\"\\uD83C\\uDFAF [CrewDropdown] Resetting selectedValue to null:\", {\n                placeholder,\n                value\n            });\n            setSelectedValue(null);\n        }\n    }, [\n        value,\n        crewList,\n        placeholder\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (trainerIdOptions.length > 0 && allCrewList.length > 0) {\n            const filteredCrewList = allCrewList.filter((crew)=>{\n                return trainerIdOptions.includes(crew.id);\n            });\n            setCrewList(filteredCrewList);\n        }\n    }, [\n        trainerIdOptions,\n        allCrewList\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (memberIdOptions.length > 0 && allCrewList.length > 0) {\n            const filteredCrewList = allCrewList.filter((crew)=>{\n                return memberIdOptions.includes(crew.id);\n            });\n            setCrewList(filteredCrewList);\n            console.log(\"\\uD83C\\uDFAF [CrewDropdown] Filtered crew list by memberIdOptions:\", {\n                memberIdOptions,\n                filteredCrewListLength: filteredCrewList.length,\n                placeholder\n            });\n        } else if (allCrewList.length > 0) {\n            // Use all crew members if no specific memberIdOptions are provided\n            setCrewList(allCrewList);\n            console.log(\"\\uD83C\\uDFAF [CrewDropdown] Using all crew members:\", {\n                allCrewListLength: allCrewList.length,\n                placeholder\n            });\n        }\n    }, [\n        memberIdOptions,\n        allCrewList,\n        placeholder\n    ]);\n    // Debug logging for trainer filter\n    console.log(\"\\uD83C\\uDFAF [CrewDropdown] Render state:\", {\n        placeholder,\n        label,\n        multi,\n        crewListLength: (crewList === null || crewList === void 0 ? void 0 : crewList.length) || 0,\n        selectedValue,\n        trainerIdOptions,\n        memberIdOptions,\n        filterByTrainingSessionMemberId,\n        isTrainerFilter: placeholder === \"Trainer\",\n        isMemberFilter: placeholder === \"Crew\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_5__.Combobox, {\n        options: crewList === null || crewList === void 0 ? void 0 : crewList.map((crew)=>({\n                value: crew.id,\n                label: \"\".concat(crew.firstName || \"\", \" \").concat(crew.surname || \"\"),\n                profile: {\n                    firstName: crew.firstName,\n                    surname: crew.surname,\n                    avatar: null\n                }\n            })),\n        value: selectedValue,\n        onChange: (selectedOption)=>{\n            // Debug logging for crew filter changes\n            console.log(\"\\uD83C\\uDFAF [CrewDropdown] onChange triggered:\", {\n                placeholder,\n                selectedOption,\n                isTrainerFilter: placeholder === \"Trainer\",\n                isMemberFilter: placeholder === \"Crew\",\n                multi,\n                previousSelectedValue: selectedValue,\n                selectedOptionType: typeof selectedOption,\n                selectedOptionIsArray: Array.isArray(selectedOption),\n                selectedOptionLength: Array.isArray(selectedOption) ? selectedOption.length : \"N/A\"\n            });\n            // selectedOption is the Option object from Combobox\n            setSelectedValue(selectedOption);\n            // Debug the data being passed to parent\n            const dataToPass = multi ? selectedOption : (selectedOption === null || selectedOption === void 0 ? void 0 : selectedOption.value) || null;\n            console.log(\"\\uD83C\\uDFAF [CrewDropdown] Calling parent onChange with:\", {\n                placeholder,\n                dataToPass,\n                isArray: Array.isArray(dataToPass),\n                dataType: typeof dataToPass,\n                dataLength: Array.isArray(dataToPass) ? dataToPass.length : \"N/A\",\n                dataValues: Array.isArray(dataToPass) ? dataToPass.map((d)=>d === null || d === void 0 ? void 0 : d.value) : dataToPass\n            });\n            // Pass the crew data to the parent component\n            onChange(dataToPass);\n        },\n        //label={label}\n        multi: multi,\n        //labelClassName=\"w-full\"\n        isLoading: !crewList,\n        placeholder: placeholder,\n        disabled: disabled\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\crew-dropdown\\\\crew-dropdown.tsx\",\n        lineNumber: 259,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewDropdown, \"ICMjbaGeOLsSbSQFK05Ssv17SgQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_7__.useLazyQuery\n    ];\n});\n_c = CrewDropdown;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewDropdown);\nvar _c;\n$RefreshReg$(_c, \"CrewDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/filter/components/crew-dropdown/crew-dropdown.tsx\n"));

/***/ })

});