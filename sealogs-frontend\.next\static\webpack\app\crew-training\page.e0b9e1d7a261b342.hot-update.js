"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts":
/*!***************************************************************!*\
  !*** ./src/app/ui/crew-training/utils/crew-training-utils.ts ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TrainingPriority: function() { return /* binding */ TrainingPriority; },\n/* harmony export */   debugTrainingData: function() { return /* binding */ debugTrainingData; },\n/* harmony export */   deduplicateTrainingData: function() { return /* binding */ deduplicateTrainingData; },\n/* harmony export */   filterTrainingDataByCategory: function() { return /* binding */ filterTrainingDataByCategory; },\n/* harmony export */   getTrainingDataStats: function() { return /* binding */ getTrainingDataStats; },\n/* harmony export */   mergeAndSortCrewTrainingData: function() { return /* binding */ mergeAndSortCrewTrainingData; },\n/* harmony export */   sortUnifiedTrainingData: function() { return /* binding */ sortUnifiedTrainingData; },\n/* harmony export */   transformCompletedTrainingToUnifiedFormat: function() { return /* binding */ transformCompletedTrainingToUnifiedFormat; },\n/* harmony export */   transformTrainingSessionDuesToUnifiedFormat: function() { return /* binding */ transformTrainingSessionDuesToUnifiedFormat; }\n/* harmony export */ });\n/* harmony import */ var _lib_actions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n\nvar TrainingPriority;\n(function(TrainingPriority) {\n    TrainingPriority[TrainingPriority[\"OVERDUE\"] = 1] = \"OVERDUE\";\n    TrainingPriority[TrainingPriority[\"UPCOMING\"] = 2] = \"UPCOMING\";\n    TrainingPriority[TrainingPriority[\"COMPLETED\"] = 3] = \"COMPLETED\";\n})(TrainingPriority || (TrainingPriority = {}));\n/**\n * Transform completed training sessions to match the unified training data format\n * Optimized version with better error handling and performance\n * @param trainingList - Array of completed training sessions\n * @param getVesselWithIcon - Function to get complete vessel data with position/icon\n * @returns Array of transformed training data\n */ const transformCompletedTrainingToUnifiedFormat = (trainingList, getVesselWithIcon)=>{\n    if (!trainingList || !Array.isArray(trainingList)) {\n        return [];\n    }\n    try {\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_members, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1;\n            // Enhanced vessel data transformation with position information\n            let completeVesselData = training.vessel || {\n                id: 0,\n                title: \"Unknown\"\n            };\n            if (getVesselWithIcon && ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id)) {\n                try {\n                    // Get complete vessel data including position, icon, and other metadata\n                    completeVesselData = getVesselWithIcon(training.vessel.id, training.vessel);\n                    // Ensure we preserve original vessel data if transformation fails\n                    if (!completeVesselData || typeof completeVesselData !== \"object\") {\n                        completeVesselData = training.vessel;\n                    }\n                    // Add position information if available\n                    if (training.vessel.position && !completeVesselData.position) {\n                        completeVesselData.position = training.vessel.position;\n                    }\n                    // Add location type if available\n                    if (training.trainingLocationType && !completeVesselData.trainingLocationType) {\n                        completeVesselData.trainingLocationType = training.trainingLocationType;\n                    }\n                } catch (error) {\n                    console.warn(\"Failed to enhance vessel data for training:\", training.id, error);\n                    completeVesselData = training.vessel;\n                }\n            }\n            // Enhanced member deduplication and normalization\n            const rawMembers = ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [];\n            const deduplicatedMembers = rawMembers.reduce((acc, member)=>{\n                // Check if member already exists in the accumulator\n                const existingMember = acc.find((m)=>m.id === member.id);\n                if (existingMember) {\n                    // Update existing member with more complete data\n                    existingMember.firstName = member.firstName || existingMember.firstName;\n                    existingMember.surname = member.surname || existingMember.surname;\n                    existingMember.email = member.email || existingMember.email;\n                } else {\n                    // Add new member with normalized data\n                    acc.push({\n                        id: member.id,\n                        firstName: member.firstName || \"\",\n                        surname: member.surname || \"\",\n                        email: member.email || \"\",\n                        ...member // Preserve any additional member data\n                    });\n                }\n                return acc;\n            }, []);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: ((_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id) || 0,\n                vessel: completeVesselData,\n                trainingTypeID: ((_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id) || 0,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    id: 0,\n                    title: \"Unknown\"\n                },\n                members: deduplicatedMembers,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                category: \"completed\",\n                originalData: training\n            };\n        });\n    } catch (error) {\n        console.error(\"Error transforming completed training data:\", error);\n        return [];\n    }\n};\n/**\n * Transform training session dues to unified format with calculated status\n * Applies the same grouping logic as CrewTrainingList\n * @param trainingSessionDues - Array of training session dues (overdue/upcoming)\n * @returns Array of transformed training data with calculated status\n */ const transformTrainingSessionDuesToUnifiedFormat = (trainingSessionDues)=>{\n    if (!trainingSessionDues || !Array.isArray(trainingSessionDues)) {\n        return [];\n    }\n    try {\n        // Apply the same filtering logic as CrewTrainingList\n        // Filter out crew members who are no longer assigned to the vessel\n        // Make the filtering more robust by checking for valid data structures\n        const filteredData = trainingSessionDues.filter((item)=>{\n            var _item_vessel_seaLogsMembers_nodes, _item_vessel_seaLogsMembers, _item_vessel;\n            // Check if item has required properties\n            if (!item || !item.memberID || !item.vesselID) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item:\", item);\n                return false;\n            }\n            // Check vessel member assignment - make this more flexible\n            const hasValidVesselMembers = (_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : (_item_vessel_seaLogsMembers = _item_vessel.seaLogsMembers) === null || _item_vessel_seaLogsMembers === void 0 ? void 0 : (_item_vessel_seaLogsMembers_nodes = _item_vessel_seaLogsMembers.nodes) === null || _item_vessel_seaLogsMembers_nodes === void 0 ? void 0 : _item_vessel_seaLogsMembers_nodes.some((m)=>{\n                return m && m.id === item.memberID;\n            });\n            // If vessel member check fails, still include the item\n            if (!hasValidVesselMembers) {\n                // For now, include all items to prevent data loss - this might need adjustment based on business logic\n                return true;\n            }\n            return true;\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After vessel member filtering:\", {\n            originalCount: trainingSessionDues.length,\n            filteredCount: filteredData.length,\n            removedCount: trainingSessionDues.length - filteredData.length\n        });\n        // Add status to each record with error handling\n        const dueWithStatus = filteredData.map((due)=>{\n            try {\n                const status = (0,_lib_actions__WEBPACK_IMPORTED_MODULE_0__.GetTrainingSessionStatus)(due);\n                return {\n                    ...due,\n                    status\n                };\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error calculating status for:\", due, error);\n                // Return with default status to prevent data loss\n                return {\n                    ...due,\n                    status: {\n                        class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                        label: \"Unknown\",\n                        isOverdue: false,\n                        dueWithinSevenDays: false\n                    }\n                };\n            }\n        });\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After status calculation:\", {\n            totalRecords: dueWithStatus.length,\n            statusBreakdown: dueWithStatus.reduce((acc, item)=>{\n                const key = item.status.isOverdue ? \"overdue\" : item.status.dueWithinSevenDays ? \"upcoming\" : \"future\";\n                acc[key] = (acc[key] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        // Apply the same grouping logic as CrewTrainingList\n        // Group by vessel-trainingType-dueDate with better error handling\n        const groupedDues = dueWithStatus.reduce((acc, due)=>{\n            try {\n                const key = \"\".concat(due.vesselID || 0, \"-\").concat(due.trainingTypeID || 0, \"-\").concat(due.dueDate || \"unknown\");\n                if (!acc[key]) {\n                    var _due_trainingSession;\n                    acc[key] = {\n                        id: due.id,\n                        vesselID: due.vesselID || 0,\n                        vessel: due.vessel || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        trainingTypeID: due.trainingTypeID || 0,\n                        trainingType: due.trainingType || {\n                            id: 0,\n                            title: \"Unknown\"\n                        },\n                        dueDate: due.dueDate,\n                        status: due.status,\n                        trainingLocationType: (_due_trainingSession = due.trainingSession) === null || _due_trainingSession === void 0 ? void 0 : _due_trainingSession.trainingLocationType,\n                        members: []\n                    };\n                }\n                if (due.member) {\n                    acc[key].members.push(due.member);\n                }\n                return acc;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error grouping due:\", due, error);\n                return acc;\n            }\n        }, {});\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] After grouping by vessel-trainingType-dueDate:\", {\n            groupCount: Object.keys(groupedDues).length,\n            groupKeys: Object.keys(groupedDues),\n            sampleGroup: Object.values(groupedDues)[0]\n        });\n        // Merge members within each group (same as CrewTrainingList) with optimization\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting member merging for groups:\", Object.values(groupedDues).length);\n        const mergedDues = Object.values(groupedDues).map((group)=>{\n            var _group_members;\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing group:\", {\n                id: group.id,\n                vesselID: group.vesselID,\n                trainingTypeID: group.trainingTypeID,\n                membersCount: ((_group_members = group.members) === null || _group_members === void 0 ? void 0 : _group_members.length) || 0,\n                members: group.members\n            });\n            // Use Map for faster member deduplication\n            const memberMap = new Map();\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing members for group:\", group.members);\n            group.members.forEach((member)=>{\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Processing member:\", member);\n                if (member && member.id) {\n                    const existingMember = memberMap.get(member.id);\n                    if (existingMember) {\n                        // Update existing member with more complete data\n                        memberMap.set(member.id, {\n                            ...existingMember,\n                            firstName: member.firstName || existingMember.firstName,\n                            surname: member.surname || existingMember.surname,\n                            email: member.email || existingMember.email,\n                            ...member // Preserve any additional member data\n                        });\n                    } else {\n                        // Add new member with normalized data\n                        memberMap.set(member.id, {\n                            id: member.id,\n                            firstName: member.firstName || \"\",\n                            surname: member.surname || \"\",\n                            email: member.email || \"\",\n                            ...member // Preserve any additional member data\n                        });\n                    }\n                } else {\n                    console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Invalid member:\", member);\n                }\n            });\n            const mergedMembers = Array.from(memberMap.values());\n            console.log(\"\\uD83D\\uDD0D [crew-training-utils] Merged members result:\", mergedMembers);\n            try {\n                var _group_status, _group_status1;\n                // Determine category based on status\n                let category;\n                if ((_group_status = group.status) === null || _group_status === void 0 ? void 0 : _group_status.isOverdue) {\n                    category = \"overdue\";\n                } else if ((_group_status1 = group.status) === null || _group_status1 === void 0 ? void 0 : _group_status1.dueWithinSevenDays) {\n                    category = \"upcoming\";\n                } else {\n                    category = \"upcoming\" // Default for future due dates\n                    ;\n                }\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Determined category:\", category, \"for status:\", group.status);\n                // Enhanced vessel data with position information\n                // Create a new object to avoid \"object is not extensible\" errors\n                const enhancedVessel = {\n                    ...group.vessel || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    // Add training location type if available\n                    ...group.trainingLocationType && {\n                        trainingLocationType: group.trainingLocationType\n                    }\n                };\n                const result = {\n                    id: group.id,\n                    dueDate: group.dueDate,\n                    vesselID: group.vesselID,\n                    vessel: enhancedVessel,\n                    trainingTypeID: group.trainingTypeID,\n                    trainingType: group.trainingType || {\n                        id: 0,\n                        title: \"Unknown\"\n                    },\n                    members: mergedMembers,\n                    status: group.status,\n                    category,\n                    originalData: group\n                };\n                console.log(\"\\uD83D\\uDD0D [crew-training-utils] Created unified record:\", result);\n                return result;\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error creating unified record:\", error, \"for group:\", group);\n                return null // Return null to filter out failed records\n                ;\n            }\n        }).filter(Boolean)// Filter out null values and cast\n        ;\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Final merged dues data:\", {\n            totalRecords: mergedDues.length,\n            categoryBreakdown: mergedDues.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sampleRecord: mergedDues[0],\n            allRecords: mergedDues\n        });\n        return mergedDues;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in transformTrainingSessionDuesToUnifiedFormat:\", error);\n        return [];\n    }\n};\n/**\n * Get priority value for sorting based on training category and status\n * @param training - Unified training data item\n * @returns Priority number (lower = higher priority)\n */ const getTrainingPriority = (training)=>{\n    switch(training.category){\n        case \"overdue\":\n            return 1;\n        case \"upcoming\":\n            return 2;\n        case \"completed\":\n            return 3;\n        default:\n            return 3;\n    }\n};\n/**\n * Remove duplicate training records based on ID with enhanced deduplication logic\n * Optimized version using Map for better performance\n * @param data - Array of unified training data\n * @returns Deduplicated array with merged member data\n */ const deduplicateTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for deduplication\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting deduplication:\", {\n        totalRecords: data.length,\n        sampleRecord: data[0]\n    });\n    try {\n        // Use Map for O(1) lookups instead of O(n) findIndex\n        const recordMap = new Map();\n        for (const item of data){\n            // Handle both string and number IDs\n            const itemId = typeof item.id === \"string\" ? parseInt(item.id, 10) : item.id;\n            if (!item || !itemId && itemId !== 0 || isNaN(itemId)) {\n                console.warn(\"\\uD83D\\uDD0D [crew-training-utils] Skipping invalid item in deduplication:\", item);\n                continue;\n            }\n            if (recordMap.has(itemId)) {\n                var // Add existing members\n                _existing_members, // Add/merge new members\n                _item_members, _item_vessel, _item_trainingType, _item_status;\n                // Merge with existing record\n                const existing = recordMap.get(itemId);\n                // Use Map for member deduplication for better performance\n                const memberMap = new Map();\n                (_existing_members = existing.members) === null || _existing_members === void 0 ? void 0 : _existing_members.forEach((member)=>{\n                    if (member && member.id) {\n                        memberMap.set(member.id, member);\n                    }\n                });\n                (_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.forEach((member)=>{\n                    if (member && member.id) {\n                        const existingMember = memberMap.get(member.id);\n                        if (existingMember) {\n                            // Merge member data, preferring non-empty values\n                            memberMap.set(member.id, {\n                                ...existingMember,\n                                firstName: member.firstName || existingMember.firstName,\n                                surname: member.surname || existingMember.surname,\n                                email: member.email || existingMember.email,\n                                ...member // Preserve any additional member data\n                            });\n                        } else {\n                            memberMap.set(member.id, member);\n                        }\n                    }\n                });\n                // Update the existing record with merged data\n                recordMap.set(itemId, {\n                    ...existing,\n                    members: Array.from(memberMap.values()),\n                    // Prefer more recent or complete data\n                    vessel: ((_item_vessel = item.vessel) === null || _item_vessel === void 0 ? void 0 : _item_vessel.title) && item.vessel.title !== \"Unknown\" ? item.vessel : existing.vessel,\n                    trainingType: ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title) && item.trainingType.title !== \"Unknown\" ? item.trainingType : existing.trainingType,\n                    // Prefer overdue status over upcoming/completed\n                    status: ((_item_status = item.status) === null || _item_status === void 0 ? void 0 : _item_status.isOverdue) ? item.status : existing.status\n                });\n            } else {\n                // Add new record\n                recordMap.set(item.id, item);\n            }\n        }\n        const deduplicatedData = Array.from(recordMap.values());\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] Deduplication complete:\", {\n            originalCount: data.length,\n            deduplicatedCount: deduplicatedData.length,\n            removedDuplicates: data.length - deduplicatedData.length,\n            categoryBreakdown: deduplicatedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {})\n        });\n        return deduplicatedData;\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in deduplication:\", error);\n        return data // Return original data if deduplication fails\n        ;\n    }\n};\n/**\n * Sort unified training data with priority-based ordering\n * Optimized version with better performance and error handling\n * @param data - Array of unified training data\n * @returns Sorted array with overdue first, then upcoming, then completed\n */ const sortUnifiedTrainingData = (data)=>{\n    if (!data || !Array.isArray(data)) {\n        console.log(\"\\uD83D\\uDD0D [crew-training-utils] No data provided for sorting\");\n        return [];\n    }\n    console.log(\"\\uD83D\\uDD0D [crew-training-utils] Starting sort:\", {\n        totalRecords: data.length,\n        categories: data.reduce((acc, item)=>{\n            acc[item.category] = (acc[item.category] || 0) + 1;\n            return acc;\n        }, {})\n    });\n    try {\n        return data.sort((a, b)=>{\n            try {\n                // First sort by priority (overdue > upcoming > completed)\n                const priorityDiff = getTrainingPriority(a) - getTrainingPriority(b);\n                if (priorityDiff !== 0) {\n                    return priorityDiff;\n                }\n                // Within same priority, sort by date with error handling\n                const dateA = a.dueDate ? new Date(a.dueDate).getTime() : 0;\n                const dateB = b.dueDate ? new Date(b.dueDate).getTime() : 0;\n                // Handle invalid dates\n                if (isNaN(dateA) && isNaN(dateB)) return 0;\n                if (isNaN(dateA)) return 1;\n                if (isNaN(dateB)) return -1;\n                if (a.category === \"overdue\") {\n                    // For overdue: most overdue first (earliest due date first)\n                    return dateA - dateB;\n                } else if (a.category === \"upcoming\") {\n                    // For upcoming: soonest due date first\n                    return dateA - dateB;\n                } else {\n                    // For completed: most recent completion first (latest date first)\n                    return dateB - dateA;\n                }\n            } catch (error) {\n                console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error comparing items in sort:\", error, {\n                    a,\n                    b\n                });\n                return 0 // Keep original order if comparison fails\n                ;\n            }\n        });\n    } catch (error) {\n        console.error(\"\\uD83D\\uDD0D [crew-training-utils] Error in sorting:\", error);\n        return data // Return original data if sorting fails\n        ;\n    }\n};\n/**\n * Main function to merge and sort crew training data from multiple sources\n * Optimized version with comprehensive debugging and error handling\n * @param options - Configuration object with data sources and utilities\n * @returns Unified and sorted training data array\n */ const mergeAndSortCrewTrainingData = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], getVesselWithIcon, includeCompleted = true, debug = false } = param;\n    console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting merge process:\", {\n        trainingSessionDuesCount: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) || 0,\n        completedTrainingListCount: (completedTrainingList === null || completedTrainingList === void 0 ? void 0 : completedTrainingList.length) || 0,\n        includeCompleted,\n        debug\n    });\n    try {\n        // Transform overdue/upcoming training data\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming training session dues...\");\n        const transformedDues = transformTrainingSessionDuesToUnifiedFormat(trainingSessionDues);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed dues result:\", {\n            count: transformedDues.length,\n            sample: transformedDues[0]\n        });\n        // Transform completed training data if requested\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transforming completed training...\");\n        const transformedCompleted = includeCompleted ? transformCompletedTrainingToUnifiedFormat(completedTrainingList, getVesselWithIcon) : [];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Transformed completed result:\", {\n            count: transformedCompleted.length,\n            sample: transformedCompleted[0]\n        });\n        // Combine all data\n        const combinedData = [\n            ...transformedDues,\n            ...transformedCompleted\n        ];\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Combined data:\", {\n            totalCount: combinedData.length,\n            duesCount: transformedDues.length,\n            completedCount: transformedCompleted.length,\n            sample: combinedData[0]\n        });\n        // Sort with priority-based ordering (deduplication removed)\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Starting sorting...\");\n        const sortedData = sortUnifiedTrainingData(combinedData);\n        console.log(\"\\uD83D\\uDD0D [mergeAndSortCrewTrainingData] Final result:\", {\n            totalCount: sortedData.length,\n            categoryBreakdown: sortedData.reduce((acc, item)=>{\n                acc[item.category] = (acc[item.category] || 0) + 1;\n                return acc;\n            }, {}),\n            sample: sortedData[0]\n        });\n        // Optional debug analysis\n        if (debug) {\n            debugTrainingData(sortedData, \"Final Merged Training Data\");\n        }\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error merging and sorting crew training data:\", error);\n        console.error(\"❌ Error stack:\", error instanceof Error ? error.stack : \"No stack trace\");\n        return [];\n    }\n};\n/**\n * Filter unified training data by category\n * @param data - Unified training data array\n * @param categories - Categories to include\n * @returns Filtered data array\n */ const filterTrainingDataByCategory = (data, categories)=>{\n    return data.filter((item)=>categories.includes(item.category));\n};\n/**\n * Get training data statistics\n * @param data - Unified training data array\n * @returns Statistics object with counts by category\n */ const getTrainingDataStats = (data)=>{\n    return {\n        total: data.length,\n        overdue: data.filter((item)=>item.category === \"overdue\").length,\n        upcoming: data.filter((item)=>item.category === \"upcoming\").length,\n        completed: data.filter((item)=>item.category === \"completed\").length\n    };\n};\n/**\n * Debug function to analyze training data for duplicates and issues\n * Only use for debugging - not for production\n * @param data - Unified training data array\n * @param label - Label for the debug output\n */ const debugTrainingData = function(data) {\n    let label = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"Training Data\";\n    if (false) {}\n    const ids = data.map((item)=>item.id);\n    const duplicateIds = ids.filter((id, index)=>ids.indexOf(id) !== index);\n    console.group(\"\\uD83D\\uDD0D \".concat(label, \" Analysis\"));\n    console.log(\"Total records:\", data.length);\n    console.log(\"Categories:\", data.reduce((acc, item)=>{\n        acc[item.category] = (acc[item.category] || 0) + 1;\n        return acc;\n    }, {}));\n    if (duplicateIds.length > 0) {\n        console.warn(\"⚠️ Duplicate IDs found:\", Array.from(new Set(duplicateIds)));\n        console.log(\"Duplicate records:\", data.filter((item)=>duplicateIds.includes(item.id)));\n    } else {\n        console.log(\"✅ No duplicates found\");\n    }\n    console.log(\"Sample records by category:\");\n    const categories = [\n        \"overdue\",\n        \"upcoming\",\n        \"completed\"\n    ];\n    categories.forEach((category)=>{\n        const sample = data.find((item)=>item.category === category);\n        if (sample) {\n            var _sample_trainingType, _sample_vessel, _sample_members;\n            console.log(\"\".concat(category, \":\"), {\n                id: sample.id,\n                trainingType: (_sample_trainingType = sample.trainingType) === null || _sample_trainingType === void 0 ? void 0 : _sample_trainingType.title,\n                dueDate: sample.dueDate,\n                vessel: (_sample_vessel = sample.vessel) === null || _sample_vessel === void 0 ? void 0 : _sample_vessel.title,\n                membersCount: ((_sample_members = sample.members) === null || _sample_members === void 0 ? void 0 : _sample_members.length) || 0\n            });\n        }\n    });\n    console.groupEnd();\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy91dGlscy9jcmV3LXRyYWluaW5nLXV0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBK0Q7O1VBcUNuREM7Ozs7R0FBQUEscUJBQUFBO0FBTVo7Ozs7OztDQU1DLEdBQ00sTUFBTUMsNENBQTRDLENBQ3JEQyxjQUNBQztJQUVBLElBQUksQ0FBQ0QsZ0JBQWdCLENBQUNFLE1BQU1DLE9BQU8sQ0FBQ0gsZUFBZTtRQUMvQyxPQUFPLEVBQUU7SUFDYjtJQUVBLElBQUk7UUFDQSxPQUFPQSxhQUFhSSxHQUFHLENBQUMsQ0FBQ0M7Z0JBSUFBLGtCQTBCTkEsbUJBMEJMQSxtQkFFTUEsZ0NBQUFBLCtCQUFBQSx5QkFDRkEsZ0NBQUFBO1lBMURsQixnRUFBZ0U7WUFDaEUsSUFBSUMscUJBQXFCRCxTQUFTRSxNQUFNLElBQUk7Z0JBQUVDLElBQUk7Z0JBQUdDLE9BQU87WUFBVTtZQUV0RSxJQUFJUix1QkFBcUJJLG1CQUFBQSxTQUFTRSxNQUFNLGNBQWZGLHVDQUFBQSxpQkFBaUJHLEVBQUUsR0FBRTtnQkFDMUMsSUFBSTtvQkFDQSx3RUFBd0U7b0JBQ3hFRixxQkFBcUJMLGtCQUFrQkksU0FBU0UsTUFBTSxDQUFDQyxFQUFFLEVBQUVILFNBQVNFLE1BQU07b0JBRTFFLGtFQUFrRTtvQkFDbEUsSUFBSSxDQUFDRCxzQkFBc0IsT0FBT0EsdUJBQXVCLFVBQVU7d0JBQy9EQSxxQkFBcUJELFNBQVNFLE1BQU07b0JBQ3hDO29CQUVBLHdDQUF3QztvQkFDeEMsSUFBSUYsU0FBU0UsTUFBTSxDQUFDRyxRQUFRLElBQUksQ0FBQ0osbUJBQW1CSSxRQUFRLEVBQUU7d0JBQzFESixtQkFBbUJJLFFBQVEsR0FBR0wsU0FBU0UsTUFBTSxDQUFDRyxRQUFRO29CQUMxRDtvQkFFQSxpQ0FBaUM7b0JBQ2pDLElBQUlMLFNBQVNNLG9CQUFvQixJQUFJLENBQUNMLG1CQUFtQkssb0JBQW9CLEVBQUU7d0JBQzNFTCxtQkFBbUJLLG9CQUFvQixHQUFHTixTQUFTTSxvQkFBb0I7b0JBQzNFO2dCQUNKLEVBQUUsT0FBT0MsT0FBTztvQkFDWkMsUUFBUUMsSUFBSSxDQUFDLCtDQUErQ1QsU0FBU0csRUFBRSxFQUFFSTtvQkFDekVOLHFCQUFxQkQsU0FBU0UsTUFBTTtnQkFDeEM7WUFDSjtZQUVBLGtEQUFrRDtZQUNsRCxNQUFNUSxhQUFhVixFQUFBQSxvQkFBQUEsU0FBU1csT0FBTyxjQUFoQlgsd0NBQUFBLGtCQUFrQlksS0FBSyxLQUFJLEVBQUU7WUFDaEQsTUFBTUMsc0JBQXNCSCxXQUFXSSxNQUFNLENBQUMsQ0FBQ0MsS0FBWUM7Z0JBQ3ZELG9EQUFvRDtnQkFDcEQsTUFBTUMsaUJBQWlCRixJQUFJRyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVoQixFQUFFLEtBQUthLE9BQU9iLEVBQUU7Z0JBRXZELElBQUljLGdCQUFnQjtvQkFDaEIsaURBQWlEO29CQUNqREEsZUFBZUcsU0FBUyxHQUFHSixPQUFPSSxTQUFTLElBQUlILGVBQWVHLFNBQVM7b0JBQ3ZFSCxlQUFlSSxPQUFPLEdBQUdMLE9BQU9LLE9BQU8sSUFBSUosZUFBZUksT0FBTztvQkFDakVKLGVBQWVLLEtBQUssR0FBR04sT0FBT00sS0FBSyxJQUFJTCxlQUFlSyxLQUFLO2dCQUMvRCxPQUFPO29CQUNILHNDQUFzQztvQkFDdENQLElBQUlRLElBQUksQ0FBQzt3QkFDTHBCLElBQUlhLE9BQU9iLEVBQUU7d0JBQ2JpQixXQUFXSixPQUFPSSxTQUFTLElBQUk7d0JBQy9CQyxTQUFTTCxPQUFPSyxPQUFPLElBQUk7d0JBQzNCQyxPQUFPTixPQUFPTSxLQUFLLElBQUk7d0JBQ3ZCLEdBQUdOLE9BQU8sc0NBQXNDO29CQUNwRDtnQkFDSjtnQkFDQSxPQUFPRDtZQUNYLEdBQUcsRUFBRTtZQUVMLE9BQU87Z0JBQ0haLElBQUlILFNBQVNHLEVBQUU7Z0JBQ2ZxQixTQUFTeEIsU0FBU3lCLElBQUk7Z0JBQ3RCQyxVQUFVMUIsRUFBQUEsb0JBQUFBLFNBQVNFLE1BQU0sY0FBZkYsd0NBQUFBLGtCQUFpQkcsRUFBRSxLQUFJO2dCQUNqQ0QsUUFBUUQ7Z0JBQ1IwQixnQkFBZ0IzQixFQUFBQSwwQkFBQUEsU0FBUzRCLGFBQWEsY0FBdEI1QiwrQ0FBQUEsZ0NBQUFBLHdCQUF3QlksS0FBSyxjQUE3QloscURBQUFBLGlDQUFBQSw2QkFBK0IsQ0FBQyxFQUFFLGNBQWxDQSxxREFBQUEsK0JBQW9DRyxFQUFFLEtBQUk7Z0JBQzFEMEIsY0FBYzdCLEVBQUFBLDJCQUFBQSxTQUFTNEIsYUFBYSxjQUF0QjVCLGdEQUFBQSxpQ0FBQUEseUJBQXdCWSxLQUFLLGNBQTdCWixxREFBQUEsOEJBQStCLENBQUMsRUFBRSxLQUFJO29CQUFFRyxJQUFJO29CQUFHQyxPQUFPO2dCQUFVO2dCQUM5RU8sU0FBU0U7Z0JBQ1RpQixRQUFRO29CQUNKQyxPQUFPO29CQUNQQyxXQUFXO29CQUNYQyxPQUFPO29CQUNQQyxvQkFBb0I7Z0JBQ3hCO2dCQUNBQyxVQUFVO2dCQUNWQyxjQUFjcEM7WUFDbEI7UUFDQTtJQUNKLEVBQUUsT0FBT08sT0FBTztRQUNaQyxRQUFRRCxLQUFLLENBQUMsK0NBQStDQTtRQUM3RCxPQUFPLEVBQUU7SUFDYjtBQUNKLEVBQUM7QUFFRDs7Ozs7Q0FLQyxHQUNNLE1BQU04Qiw4Q0FBOEMsQ0FDdkRDO0lBRUEsSUFBSSxDQUFDQSx1QkFBdUIsQ0FBQ3pDLE1BQU1DLE9BQU8sQ0FBQ3dDLHNCQUFzQjtRQUM3RCxPQUFPLEVBQUU7SUFDYjtJQUVBLElBQUk7UUFDQSxxREFBcUQ7UUFDckQsbUVBQW1FO1FBQ25FLHVFQUF1RTtRQUN2RSxNQUFNQyxlQUFlRCxvQkFBb0JFLE1BQU0sQ0FBQyxDQUFDQztnQkFRZkEsbUNBQUFBLDZCQUFBQTtZQVA5Qix3Q0FBd0M7WUFDeEMsSUFBSSxDQUFDQSxRQUFRLENBQUNBLEtBQUtDLFFBQVEsSUFBSSxDQUFDRCxLQUFLZixRQUFRLEVBQUU7Z0JBQzNDbEIsUUFBUUMsSUFBSSxDQUFDLDZEQUFtRGdDO2dCQUNoRSxPQUFPO1lBQ1g7WUFFQSwyREFBMkQ7WUFDM0QsTUFBTUUseUJBQXdCRixlQUFBQSxLQUFLdkMsTUFBTSxjQUFYdUMsb0NBQUFBLDhCQUFBQSxhQUFhRyxjQUFjLGNBQTNCSCxtREFBQUEsb0NBQUFBLDRCQUE2QjdCLEtBQUssY0FBbEM2Qix3REFBQUEsa0NBQW9DSSxJQUFJLENBQUMsQ0FBQzFCO2dCQUNwRSxPQUFPQSxLQUFLQSxFQUFFaEIsRUFBRSxLQUFLc0MsS0FBS0MsUUFBUTtZQUN0QztZQUVBLHVEQUF1RDtZQUN2RCxJQUFJLENBQUNDLHVCQUF1QjtnQkFDeEIsdUdBQXVHO2dCQUN2RyxPQUFPO1lBQ1g7WUFFQSxPQUFPO1FBQ1g7UUFFQW5DLFFBQVFzQyxHQUFHLENBQUMscUVBQTJEO1lBQ25FQyxlQUFlVCxvQkFBb0JVLE1BQU07WUFDekNDLGVBQWVWLGFBQWFTLE1BQU07WUFDbENFLGNBQWNaLG9CQUFvQlUsTUFBTSxHQUFHVCxhQUFhUyxNQUFNO1FBQ2xFO1FBRUEsZ0RBQWdEO1FBQ2hELE1BQU1HLGdCQUFnQlosYUFBYXhDLEdBQUcsQ0FBQyxDQUFDcUQ7WUFDcEMsSUFBSTtnQkFDQSxNQUFNdEIsU0FBU3RDLHNFQUF3QkEsQ0FBQzREO2dCQUN4QyxPQUFPO29CQUFFLEdBQUdBLEdBQUc7b0JBQUV0QjtnQkFBTztZQUM1QixFQUFFLE9BQU92QixPQUFPO2dCQUNaQyxRQUFRRCxLQUFLLENBQUMsb0VBQTBENkMsS0FBSzdDO2dCQUM3RSxrREFBa0Q7Z0JBQ2xELE9BQU87b0JBQ0gsR0FBRzZDLEdBQUc7b0JBQ050QixRQUFRO3dCQUNKRyxPQUFPO3dCQUNQRixPQUFPO3dCQUNQQyxXQUFXO3dCQUNYRSxvQkFBb0I7b0JBQ3hCO2dCQUNKO1lBQ0o7UUFDSjtRQUVBMUIsUUFBUXNDLEdBQUcsQ0FBQyxnRUFBc0Q7WUFDOURPLGNBQWNGLGNBQWNILE1BQU07WUFDbENNLGlCQUFpQkgsY0FBY3JDLE1BQU0sQ0FBQyxDQUFDQyxLQUFVMEI7Z0JBQzdDLE1BQU1jLE1BQU1kLEtBQUtYLE1BQU0sQ0FBQ0UsU0FBUyxHQUFHLFlBQ3pCUyxLQUFLWCxNQUFNLENBQUNJLGtCQUFrQixHQUFHLGFBQWE7Z0JBQ3pEbkIsR0FBRyxDQUFDd0MsSUFBSSxHQUFHLENBQUN4QyxHQUFHLENBQUN3QyxJQUFJLElBQUksS0FBSztnQkFDN0IsT0FBT3hDO1lBQ1gsR0FBRyxDQUFDO1FBQ1I7UUFFQSxvREFBb0Q7UUFDcEQsa0VBQWtFO1FBQ2xFLE1BQU15QyxjQUFjTCxjQUFjckMsTUFBTSxDQUNwQyxDQUFDQyxLQUFVcUM7WUFDUCxJQUFJO2dCQUNBLE1BQU1HLE1BQU0sR0FBd0JILE9BQXJCQSxJQUFJMUIsUUFBUSxJQUFJLEdBQUUsS0FBOEIwQixPQUEzQkEsSUFBSXpCLGNBQWMsSUFBSSxHQUFFLEtBQTRCLE9BQXpCeUIsSUFBSTVCLE9BQU8sSUFBSTtnQkFDOUUsSUFBSSxDQUFDVCxHQUFHLENBQUN3QyxJQUFJLEVBQUU7d0JBU2VIO29CQVIxQnJDLEdBQUcsQ0FBQ3dDLElBQUksR0FBRzt3QkFDUHBELElBQUlpRCxJQUFJakQsRUFBRTt3QkFDVnVCLFVBQVUwQixJQUFJMUIsUUFBUSxJQUFJO3dCQUMxQnhCLFFBQVFrRCxJQUFJbEQsTUFBTSxJQUFJOzRCQUFFQyxJQUFJOzRCQUFHQyxPQUFPO3dCQUFVO3dCQUNoRHVCLGdCQUFnQnlCLElBQUl6QixjQUFjLElBQUk7d0JBQ3RDRSxjQUFjdUIsSUFBSXZCLFlBQVksSUFBSTs0QkFBRTFCLElBQUk7NEJBQUdDLE9BQU87d0JBQVU7d0JBQzVEb0IsU0FBUzRCLElBQUk1QixPQUFPO3dCQUNwQk0sUUFBUXNCLElBQUl0QixNQUFNO3dCQUNsQnhCLG9CQUFvQixHQUFFOEMsdUJBQUFBLElBQUlLLGVBQWUsY0FBbkJMLDJDQUFBQSxxQkFBcUI5QyxvQkFBb0I7d0JBQy9ESyxTQUFTLEVBQUU7b0JBQ2Y7Z0JBQ0o7Z0JBQ0EsSUFBSXlDLElBQUlwQyxNQUFNLEVBQUU7b0JBQ1pELEdBQUcsQ0FBQ3dDLElBQUksQ0FBQzVDLE9BQU8sQ0FBQ1ksSUFBSSxDQUFDNkIsSUFBSXBDLE1BQU07Z0JBQ3BDO2dCQUNBLE9BQU9EO1lBQ1gsRUFBRSxPQUFPUixPQUFPO2dCQUNaQyxRQUFRRCxLQUFLLENBQUMsMERBQWdENkMsS0FBSzdDO2dCQUNuRSxPQUFPUTtZQUNYO1FBQ0osR0FDQSxDQUFDO1FBR0xQLFFBQVFzQyxHQUFHLENBQUMscUZBQTJFO1lBQ25GWSxZQUFZQyxPQUFPQyxJQUFJLENBQUNKLGFBQWFSLE1BQU07WUFDM0NhLFdBQVdGLE9BQU9DLElBQUksQ0FBQ0o7WUFDdkJNLGFBQWFILE9BQU9JLE1BQU0sQ0FBQ1AsWUFBWSxDQUFDLEVBQUU7UUFDOUM7UUFFQSwrRUFBK0U7UUFDL0VoRCxRQUFRc0MsR0FBRyxDQUFDLDBFQUFnRWEsT0FBT0ksTUFBTSxDQUFDUCxhQUFhUixNQUFNO1FBQzdHLE1BQU1nQixhQUFhTCxPQUFPSSxNQUFNLENBQUNQLGFBQWF6RCxHQUFHLENBQUMsQ0FBQ2tFO2dCQUs3QkE7WUFKbEJ6RCxRQUFRc0MsR0FBRyxDQUFDLHdEQUE4QztnQkFDdEQzQyxJQUFJOEQsTUFBTTlELEVBQUU7Z0JBQ1p1QixVQUFVdUMsTUFBTXZDLFFBQVE7Z0JBQ3hCQyxnQkFBZ0JzQyxNQUFNdEMsY0FBYztnQkFDcEN1QyxjQUFjRCxFQUFBQSxpQkFBQUEsTUFBTXRELE9BQU8sY0FBYnNELHFDQUFBQSxlQUFlakIsTUFBTSxLQUFJO2dCQUN2Q3JDLFNBQVNzRCxNQUFNdEQsT0FBTztZQUMxQjtZQUNBLDBDQUEwQztZQUMxQyxNQUFNd0QsWUFBWSxJQUFJQztZQUV0QjVELFFBQVFzQyxHQUFHLENBQUMsb0VBQTBEbUIsTUFBTXRELE9BQU87WUFFbkZzRCxNQUFNdEQsT0FBTyxDQUFDMEQsT0FBTyxDQUFDLENBQUNyRDtnQkFDbkJSLFFBQVFzQyxHQUFHLENBQUMseURBQStDOUI7Z0JBQzNELElBQUlBLFVBQVVBLE9BQU9iLEVBQUUsRUFBRTtvQkFDckIsTUFBTWMsaUJBQWlCa0QsVUFBVUcsR0FBRyxDQUFDdEQsT0FBT2IsRUFBRTtvQkFDOUMsSUFBSWMsZ0JBQWdCO3dCQUNoQixpREFBaUQ7d0JBQ2pEa0QsVUFBVUksR0FBRyxDQUFDdkQsT0FBT2IsRUFBRSxFQUFFOzRCQUNyQixHQUFHYyxjQUFjOzRCQUNqQkcsV0FBV0osT0FBT0ksU0FBUyxJQUFJSCxlQUFlRyxTQUFTOzRCQUN2REMsU0FBU0wsT0FBT0ssT0FBTyxJQUFJSixlQUFlSSxPQUFPOzRCQUNqREMsT0FBT04sT0FBT00sS0FBSyxJQUFJTCxlQUFlSyxLQUFLOzRCQUMzQyxHQUFHTixPQUFPLHNDQUFzQzt3QkFDcEQ7b0JBQ0osT0FBTzt3QkFDSCxzQ0FBc0M7d0JBQ3RDbUQsVUFBVUksR0FBRyxDQUFDdkQsT0FBT2IsRUFBRSxFQUFFOzRCQUNyQkEsSUFBSWEsT0FBT2IsRUFBRTs0QkFDYmlCLFdBQVdKLE9BQU9JLFNBQVMsSUFBSTs0QkFDL0JDLFNBQVNMLE9BQU9LLE9BQU8sSUFBSTs0QkFDM0JDLE9BQU9OLE9BQU9NLEtBQUssSUFBSTs0QkFDdkIsR0FBR04sT0FBTyxzQ0FBc0M7d0JBQ3BEO29CQUNKO2dCQUNKLE9BQU87b0JBQ0hSLFFBQVFDLElBQUksQ0FBQyxzREFBNENPO2dCQUM3RDtZQUNKO1lBRUEsTUFBTXdELGdCQUFnQjNFLE1BQU00RSxJQUFJLENBQUNOLFVBQVVKLE1BQU07WUFDakR2RCxRQUFRc0MsR0FBRyxDQUFDLDZEQUFtRDBCO1lBRS9ELElBQUk7b0JBR0lQLGVBRU9BO2dCQUpYLHFDQUFxQztnQkFDckMsSUFBSTlCO2dCQUNKLEtBQUk4QixnQkFBQUEsTUFBTW5DLE1BQU0sY0FBWm1DLG9DQUFBQSxjQUFjakMsU0FBUyxFQUFFO29CQUN6QkcsV0FBVztnQkFDZixPQUFPLEtBQUk4QixpQkFBQUEsTUFBTW5DLE1BQU0sY0FBWm1DLHFDQUFBQSxlQUFjL0Isa0JBQWtCLEVBQUU7b0JBQ3pDQyxXQUFXO2dCQUNmLE9BQU87b0JBQ0hBLFdBQVcsV0FBVywrQkFBK0I7O2dCQUN6RDtnQkFFQTNCLFFBQVFzQyxHQUFHLENBQUMsMkRBQWlEWCxVQUFVLGVBQWU4QixNQUFNbkMsTUFBTTtnQkFFbEcsaURBQWlEO2dCQUNqRCxpRUFBaUU7Z0JBQ2pFLE1BQU00QyxpQkFBaUI7b0JBQ25CLEdBQUlULE1BQU0vRCxNQUFNLElBQUk7d0JBQUVDLElBQUk7d0JBQUdDLE9BQU87b0JBQVUsQ0FBQztvQkFDL0MsMENBQTBDO29CQUMxQyxHQUFJNkQsTUFBTTNELG9CQUFvQixJQUFJO3dCQUFFQSxzQkFBc0IyRCxNQUFNM0Qsb0JBQW9CO29CQUFDLENBQUM7Z0JBQzFGO2dCQUVBLE1BQU1xRSxTQUFTO29CQUNYeEUsSUFBSThELE1BQU05RCxFQUFFO29CQUNacUIsU0FBU3lDLE1BQU16QyxPQUFPO29CQUN0QkUsVUFBVXVDLE1BQU12QyxRQUFRO29CQUN4QnhCLFFBQVF3RTtvQkFDUi9DLGdCQUFnQnNDLE1BQU10QyxjQUFjO29CQUNwQ0UsY0FBY29DLE1BQU1wQyxZQUFZLElBQUk7d0JBQUUxQixJQUFJO3dCQUFHQyxPQUFPO29CQUFVO29CQUM5RE8sU0FBUzZEO29CQUNUMUMsUUFBUW1DLE1BQU1uQyxNQUFNO29CQUNwQks7b0JBQ0FDLGNBQWM2QjtnQkFDbEI7Z0JBRUF6RCxRQUFRc0MsR0FBRyxDQUFDLDhEQUFvRDZCO2dCQUNoRSxPQUFPQTtZQUNYLEVBQUUsT0FBT3BFLE9BQU87Z0JBQ1pDLFFBQVFELEtBQUssQ0FBQyxxRUFBMkRBLE9BQU8sY0FBYzBEO2dCQUM5RixPQUFPLEtBQUssMkNBQTJDOztZQUMzRDtRQUNKLEdBQUd6QixNQUFNLENBQUNvQyxRQUFrQyxrQ0FBa0M7O1FBRTlFcEUsUUFBUXNDLEdBQUcsQ0FBQyw4REFBb0Q7WUFDNURPLGNBQWNXLFdBQVdoQixNQUFNO1lBQy9CNkIsbUJBQW1CYixXQUFXbEQsTUFBTSxDQUFDLENBQUNDLEtBQVUwQjtnQkFDNUMxQixHQUFHLENBQUMwQixLQUFLTixRQUFRLENBQUMsR0FBRyxDQUFDcEIsR0FBRyxDQUFDMEIsS0FBS04sUUFBUSxDQUFDLElBQUksS0FBSztnQkFDakQsT0FBT3BCO1lBQ1gsR0FBRyxDQUFDO1lBQ0orRCxjQUFjZCxVQUFVLENBQUMsRUFBRTtZQUMzQmUsWUFBWWY7UUFDaEI7UUFFQSxPQUFPQTtJQUNYLEVBQUUsT0FBT3pELE9BQU87UUFDWkMsUUFBUUQsS0FBSyxDQUFDLDRGQUFrRkE7UUFDaEcsT0FBTyxFQUFFO0lBQ2I7QUFDSixFQUFDO0FBRUQ7Ozs7Q0FJQyxHQUNELE1BQU15RSxzQkFBc0IsQ0FBQ2hGO0lBQ3pCLE9BQVFBLFNBQVNtQyxRQUFRO1FBQ3JCLEtBQUs7WUFDRDtRQUNKLEtBQUs7WUFDRDtRQUNKLEtBQUs7WUFDRDtRQUNKO1lBQ0k7SUFDUjtBQUNKO0FBRUE7Ozs7O0NBS0MsR0FDTSxNQUFNOEMsMEJBQTBCLENBQUNDO0lBQ3BDLElBQUksQ0FBQ0EsUUFBUSxDQUFDckYsTUFBTUMsT0FBTyxDQUFDb0YsT0FBTztRQUMvQjFFLFFBQVFzQyxHQUFHLENBQUM7UUFDWixPQUFPLEVBQUU7SUFDYjtJQUVBdEMsUUFBUXNDLEdBQUcsQ0FBQyw4REFBb0Q7UUFDNURPLGNBQWM2QixLQUFLbEMsTUFBTTtRQUN6QjhCLGNBQWNJLElBQUksQ0FBQyxFQUFFO0lBQ3pCO0lBRUEsSUFBSTtRQUNBLHFEQUFxRDtRQUNyRCxNQUFNQyxZQUFZLElBQUlmO1FBRXRCLEtBQUssTUFBTTNCLFFBQVF5QyxLQUFNO1lBQ3JCLG9DQUFvQztZQUNwQyxNQUFNRSxTQUFTLE9BQU8zQyxLQUFLdEMsRUFBRSxLQUFLLFdBQVdrRixTQUFTNUMsS0FBS3RDLEVBQUUsRUFBRSxNQUFNc0MsS0FBS3RDLEVBQUU7WUFFNUUsSUFBSSxDQUFDc0MsUUFBUyxDQUFDMkMsVUFBVUEsV0FBVyxLQUFNRSxNQUFNRixTQUFTO2dCQUNyRDVFLFFBQVFDLElBQUksQ0FBQyw4RUFBb0VnQztnQkFDakY7WUFDSjtZQUVBLElBQUkwQyxVQUFVSSxHQUFHLENBQUNILFNBQVM7b0JBT3ZCLHVCQUF1QjtnQkFDdkJJLG1CQU1BLHdCQUF3QjtnQkFDeEIvQyxlQXVCYUEsY0FDTUEsb0JBRVBBO2dCQXhDWiw2QkFBNkI7Z0JBQzdCLE1BQU0rQyxXQUFXTCxVQUFVYixHQUFHLENBQUNjO2dCQUUvQiwwREFBMEQ7Z0JBQzFELE1BQU1qQixZQUFZLElBQUlDO2lCQUd0Qm9CLG9CQUFBQSxTQUFTN0UsT0FBTyxjQUFoQjZFLHdDQUFBQSxrQkFBa0JuQixPQUFPLENBQUNyRCxDQUFBQTtvQkFDdEIsSUFBSUEsVUFBVUEsT0FBT2IsRUFBRSxFQUFFO3dCQUNyQmdFLFVBQVVJLEdBQUcsQ0FBQ3ZELE9BQU9iLEVBQUUsRUFBRWE7b0JBQzdCO2dCQUNKO2lCQUdBeUIsZ0JBQUFBLEtBQUs5QixPQUFPLGNBQVo4QixvQ0FBQUEsY0FBYzRCLE9BQU8sQ0FBQ3JELENBQUFBO29CQUNsQixJQUFJQSxVQUFVQSxPQUFPYixFQUFFLEVBQUU7d0JBQ3JCLE1BQU1jLGlCQUFpQmtELFVBQVVHLEdBQUcsQ0FBQ3RELE9BQU9iLEVBQUU7d0JBQzlDLElBQUljLGdCQUFnQjs0QkFDaEIsaURBQWlEOzRCQUNqRGtELFVBQVVJLEdBQUcsQ0FBQ3ZELE9BQU9iLEVBQUUsRUFBRTtnQ0FDckIsR0FBR2MsY0FBYztnQ0FDakJHLFdBQVdKLE9BQU9JLFNBQVMsSUFBSUgsZUFBZUcsU0FBUztnQ0FDdkRDLFNBQVNMLE9BQU9LLE9BQU8sSUFBSUosZUFBZUksT0FBTztnQ0FDakRDLE9BQU8sT0FBZ0JBLEtBQUssSUFBSSxlQUF3QkEsS0FBSztnQ0FDN0QsR0FBR04sT0FBTyxzQ0FBc0M7NEJBQ3BEO3dCQUNKLE9BQU87NEJBQ0htRCxVQUFVSSxHQUFHLENBQUN2RCxPQUFPYixFQUFFLEVBQUVhO3dCQUM3QjtvQkFDSjtnQkFDSjtnQkFFQSw4Q0FBOEM7Z0JBQzlDbUUsVUFBVVosR0FBRyxDQUFDYSxRQUFRO29CQUNsQixHQUFHSSxRQUFRO29CQUNYN0UsU0FBU2QsTUFBTTRFLElBQUksQ0FBQ04sVUFBVUosTUFBTTtvQkFDcEMsc0NBQXNDO29CQUN0QzdELFFBQVEsRUFBQ3VDLGVBQUFBLEtBQUt2QyxNQUFNLGNBQVh1QyxtQ0FBQUEsYUFBYXJDLEtBQUssS0FBSXFDLEtBQUt2QyxNQUFNLENBQUNFLEtBQUssS0FBSyxZQUFhcUMsS0FBS3ZDLE1BQU0sR0FBR3NGLFNBQVN0RixNQUFNO29CQUMvRjJCLGNBQWMsRUFBQ1kscUJBQUFBLEtBQUtaLFlBQVksY0FBakJZLHlDQUFBQSxtQkFBbUJyQyxLQUFLLEtBQUlxQyxLQUFLWixZQUFZLENBQUN6QixLQUFLLEtBQUssWUFBYXFDLEtBQUtaLFlBQVksR0FBRzJELFNBQVMzRCxZQUFZO29CQUM3SCxnREFBZ0Q7b0JBQ2hEQyxRQUFRVyxFQUFBQSxlQUFBQSxLQUFLWCxNQUFNLGNBQVhXLG1DQUFBQSxhQUFhVCxTQUFTLElBQUdTLEtBQUtYLE1BQU0sR0FBRzBELFNBQVMxRCxNQUFNO2dCQUNsRTtZQUNKLE9BQU87Z0JBQ0gsaUJBQWlCO2dCQUNqQnFELFVBQVVaLEdBQUcsQ0FBQzlCLEtBQUt0QyxFQUFFLEVBQUVzQztZQUMzQjtRQUNKO1FBRUEsTUFBTWdELG1CQUFtQjVGLE1BQU00RSxJQUFJLENBQUNVLFVBQVVwQixNQUFNO1FBRXBEdkQsUUFBUXNDLEdBQUcsQ0FBQyw4REFBb0Q7WUFDNURDLGVBQWVtQyxLQUFLbEMsTUFBTTtZQUMxQjBDLG1CQUFtQkQsaUJBQWlCekMsTUFBTTtZQUMxQzJDLG1CQUFtQlQsS0FBS2xDLE1BQU0sR0FBR3lDLGlCQUFpQnpDLE1BQU07WUFDeEQ2QixtQkFBbUJZLGlCQUFpQjNFLE1BQU0sQ0FBQyxDQUFDQyxLQUFVMEI7Z0JBQ2xEMUIsR0FBRyxDQUFDMEIsS0FBS04sUUFBUSxDQUFDLEdBQUcsQ0FBQ3BCLEdBQUcsQ0FBQzBCLEtBQUtOLFFBQVEsQ0FBQyxJQUFJLEtBQUs7Z0JBQ2pELE9BQU9wQjtZQUNYLEdBQUcsQ0FBQztRQUNSO1FBRUEsT0FBTzBFO0lBQ1gsRUFBRSxPQUFPbEYsT0FBTztRQUNaQyxRQUFRRCxLQUFLLENBQUMsOERBQW9EQTtRQUNsRSxPQUFPMkUsS0FBSyw4Q0FBOEM7O0lBQzlEO0FBQ0osRUFBQztBQUVEOzs7OztDQUtDLEdBQ00sTUFBTVUsMEJBQTBCLENBQUNWO0lBQ3BDLElBQUksQ0FBQ0EsUUFBUSxDQUFDckYsTUFBTUMsT0FBTyxDQUFDb0YsT0FBTztRQUMvQjFFLFFBQVFzQyxHQUFHLENBQUM7UUFDWixPQUFPLEVBQUU7SUFDYjtJQUVBdEMsUUFBUXNDLEdBQUcsQ0FBQyxxREFBMkM7UUFDbkRPLGNBQWM2QixLQUFLbEMsTUFBTTtRQUN6QjZDLFlBQVlYLEtBQUtwRSxNQUFNLENBQUMsQ0FBQ0MsS0FBVTBCO1lBQy9CMUIsR0FBRyxDQUFDMEIsS0FBS04sUUFBUSxDQUFDLEdBQUcsQ0FBQ3BCLEdBQUcsQ0FBQzBCLEtBQUtOLFFBQVEsQ0FBQyxJQUFJLEtBQUs7WUFDakQsT0FBT3BCO1FBQ1gsR0FBRyxDQUFDO0lBQ1I7SUFFQSxJQUFJO1FBQ0EsT0FBT21FLEtBQUtZLElBQUksQ0FBQyxDQUFDQyxHQUFHQztZQUNqQixJQUFJO2dCQUNBLDBEQUEwRDtnQkFDMUQsTUFBTUMsZUFBZWpCLG9CQUFvQmUsS0FBS2Ysb0JBQW9CZ0I7Z0JBQ2xFLElBQUlDLGlCQUFpQixHQUFHO29CQUNwQixPQUFPQTtnQkFDWDtnQkFFQSx5REFBeUQ7Z0JBQ3pELE1BQU1DLFFBQVFILEVBQUV2RSxPQUFPLEdBQUcsSUFBSTJFLEtBQUtKLEVBQUV2RSxPQUFPLEVBQUU0RSxPQUFPLEtBQUs7Z0JBQzFELE1BQU1DLFFBQVFMLEVBQUV4RSxPQUFPLEdBQUcsSUFBSTJFLEtBQUtILEVBQUV4RSxPQUFPLEVBQUU0RSxPQUFPLEtBQUs7Z0JBRTFELHVCQUF1QjtnQkFDdkIsSUFBSWQsTUFBTVksVUFBVVosTUFBTWUsUUFBUSxPQUFPO2dCQUN6QyxJQUFJZixNQUFNWSxRQUFRLE9BQU87Z0JBQ3pCLElBQUlaLE1BQU1lLFFBQVEsT0FBTyxDQUFDO2dCQUUxQixJQUFJTixFQUFFNUQsUUFBUSxLQUFLLFdBQVc7b0JBQzFCLDREQUE0RDtvQkFDNUQsT0FBTytELFFBQVFHO2dCQUNuQixPQUFPLElBQUlOLEVBQUU1RCxRQUFRLEtBQUssWUFBWTtvQkFDbEMsdUNBQXVDO29CQUN2QyxPQUFPK0QsUUFBUUc7Z0JBQ25CLE9BQU87b0JBQ0gsa0VBQWtFO29CQUNsRSxPQUFPQSxRQUFRSDtnQkFDbkI7WUFDSixFQUFFLE9BQU8zRixPQUFPO2dCQUNaQyxRQUFRRCxLQUFLLENBQUMscUVBQTJEQSxPQUFPO29CQUFFd0Y7b0JBQUdDO2dCQUFFO2dCQUN2RixPQUFPLEVBQUUsMENBQTBDOztZQUN2RDtRQUNKO0lBQ0osRUFBRSxPQUFPekYsT0FBTztRQUNaQyxRQUFRRCxLQUFLLENBQUMsd0RBQThDQTtRQUM1RCxPQUFPMkUsS0FBSyx3Q0FBd0M7O0lBQ3hEO0FBQ0osRUFBQztBQUVEOzs7OztDQUtDLEdBQ00sTUFBTW9CLCtCQUErQjtRQUFDLEVBQ3pDaEUsc0JBQXNCLEVBQUUsRUFDeEJpRSx3QkFBd0IsRUFBRSxFQUMxQjNHLGlCQUFpQixFQUNqQjRHLG1CQUFtQixJQUFJLEVBQ3ZCQyxRQUFRLEtBQUssRUFPaEI7SUFDR2pHLFFBQVFzQyxHQUFHLENBQUMsdUVBQTZEO1FBQ3JFNEQsMEJBQTBCcEUsQ0FBQUEsZ0NBQUFBLDBDQUFBQSxvQkFBcUJVLE1BQU0sS0FBSTtRQUN6RDJELDRCQUE0QkosQ0FBQUEsa0NBQUFBLDRDQUFBQSxzQkFBdUJ2RCxNQUFNLEtBQUk7UUFDN0R3RDtRQUNBQztJQUNKO0lBRUEsSUFBSTtRQUNBLDJDQUEyQztRQUMzQ2pHLFFBQVFzQyxHQUFHLENBQUM7UUFDWixNQUFNOEQsa0JBQWtCdkUsNENBQTRDQztRQUNwRTlCLFFBQVFzQyxHQUFHLENBQUMsd0VBQThEO1lBQ3RFK0QsT0FBT0QsZ0JBQWdCNUQsTUFBTTtZQUM3QjhELFFBQVFGLGVBQWUsQ0FBQyxFQUFFO1FBQzlCO1FBRUEsaURBQWlEO1FBQ2pEcEcsUUFBUXNDLEdBQUcsQ0FBQztRQUNaLE1BQU1pRSx1QkFBdUJQLG1CQUN2QjlHLDBDQUEwQzZHLHVCQUF1QjNHLHFCQUNqRSxFQUFFO1FBQ1JZLFFBQVFzQyxHQUFHLENBQUMsNkVBQW1FO1lBQzNFK0QsT0FBT0UscUJBQXFCL0QsTUFBTTtZQUNsQzhELFFBQVFDLG9CQUFvQixDQUFDLEVBQUU7UUFDbkM7UUFFQSxtQkFBbUI7UUFDbkIsTUFBTUMsZUFBZTtlQUFJSjtlQUFvQkc7U0FBcUI7UUFDbEV2RyxRQUFRc0MsR0FBRyxDQUFDLDhEQUFvRDtZQUM1RG1FLFlBQVlELGFBQWFoRSxNQUFNO1lBQy9Ca0UsV0FBV04sZ0JBQWdCNUQsTUFBTTtZQUNqQ21FLGdCQUFnQkoscUJBQXFCL0QsTUFBTTtZQUMzQzhELFFBQVFFLFlBQVksQ0FBQyxFQUFFO1FBQzNCO1FBRUEsNERBQTREO1FBQzVEeEcsUUFBUXNDLEdBQUcsQ0FBQztRQUNaLE1BQU1zRSxhQUFheEIsd0JBQXdCb0I7UUFDM0N4RyxRQUFRc0MsR0FBRyxDQUFDLDZEQUFtRDtZQUMzRG1FLFlBQVlHLFdBQVdwRSxNQUFNO1lBQzdCNkIsbUJBQW1CdUMsV0FBV3RHLE1BQU0sQ0FBQyxDQUFDQyxLQUFVMEI7Z0JBQzVDMUIsR0FBRyxDQUFDMEIsS0FBS04sUUFBUSxDQUFDLEdBQUcsQ0FBQ3BCLEdBQUcsQ0FBQzBCLEtBQUtOLFFBQVEsQ0FBQyxJQUFJLEtBQUs7Z0JBQ2pELE9BQU9wQjtZQUNYLEdBQUcsQ0FBQztZQUNKK0YsUUFBUU0sVUFBVSxDQUFDLEVBQUU7UUFDekI7UUFFQSwwQkFBMEI7UUFDMUIsSUFBSVgsT0FBTztZQUNQWSxrQkFBa0JELFlBQVk7UUFDbEM7UUFFQSxPQUFPQTtJQUNYLEVBQUUsT0FBTzdHLE9BQU87UUFDWkMsUUFBUUQsS0FBSyxDQUFDLG1EQUFtREE7UUFDakVDLFFBQVFELEtBQUssQ0FBQyxrQkFBa0JBLGlCQUFpQitHLFFBQVEvRyxNQUFNZ0gsS0FBSyxHQUFHO1FBQ3ZFLE9BQU8sRUFBRTtJQUNiO0FBQ0osRUFBQztBQUVEOzs7OztDQUtDLEdBQ00sTUFBTUMsK0JBQStCLENBQ3hDdEMsTUFDQVc7SUFFQSxPQUFPWCxLQUFLMUMsTUFBTSxDQUFDQyxDQUFBQSxPQUFRb0QsV0FBVzRCLFFBQVEsQ0FBQ2hGLEtBQUtOLFFBQVE7QUFDaEUsRUFBQztBQUVEOzs7O0NBSUMsR0FDTSxNQUFNdUYsdUJBQXVCLENBQUN4QztJQUNqQyxPQUFPO1FBQ0h5QyxPQUFPekMsS0FBS2xDLE1BQU07UUFDbEI0RSxTQUFTMUMsS0FBSzFDLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS04sUUFBUSxLQUFLLFdBQVdhLE1BQU07UUFDaEU2RSxVQUFVM0MsS0FBSzFDLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS04sUUFBUSxLQUFLLFlBQVlhLE1BQU07UUFDbEU4RSxXQUFXNUMsS0FBSzFDLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS04sUUFBUSxLQUFLLGFBQWFhLE1BQU07SUFDeEU7QUFDSixFQUFDO0FBRUQ7Ozs7O0NBS0MsR0FDTSxNQUFNcUUsb0JBQW9CLFNBQUNuQztRQUE2Qm5ELHlFQUFnQjtJQUMzRSxJQUFJZ0csS0FBeUIsRUFBZTtJQUU1QyxNQUFNQyxNQUFNOUMsS0FBS25GLEdBQUcsQ0FBQzBDLENBQUFBLE9BQVFBLEtBQUt0QyxFQUFFO0lBQ3BDLE1BQU04SCxlQUFlRCxJQUFJeEYsTUFBTSxDQUFDLENBQUNyQyxJQUFJK0gsUUFBVUYsSUFBSUcsT0FBTyxDQUFDaEksUUFBUStIO0lBRW5FMUgsUUFBUXlELEtBQUssQ0FBQyxnQkFBWSxPQUFObEMsT0FBTTtJQUMxQnZCLFFBQVFzQyxHQUFHLENBQUMsa0JBQWtCb0MsS0FBS2xDLE1BQU07SUFDekN4QyxRQUFRc0MsR0FBRyxDQUFDLGVBQWVvQyxLQUFLcEUsTUFBTSxDQUFDLENBQUNDLEtBQUswQjtRQUN6QzFCLEdBQUcsQ0FBQzBCLEtBQUtOLFFBQVEsQ0FBQyxHQUFHLENBQUNwQixHQUFHLENBQUMwQixLQUFLTixRQUFRLENBQUMsSUFBSSxLQUFLO1FBQ2pELE9BQU9wQjtJQUNYLEdBQUcsQ0FBQztJQUVKLElBQUlrSCxhQUFhakYsTUFBTSxHQUFHLEdBQUc7UUFDekJ4QyxRQUFRQyxJQUFJLENBQUMsMkJBQTJCWixNQUFNNEUsSUFBSSxDQUFDLElBQUkyRCxJQUFJSDtRQUMzRHpILFFBQVFzQyxHQUFHLENBQUMsc0JBQXNCb0MsS0FBSzFDLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUXdGLGFBQWFSLFFBQVEsQ0FBQ2hGLEtBQUt0QyxFQUFFO0lBQ3ZGLE9BQU87UUFDSEssUUFBUXNDLEdBQUcsQ0FBQztJQUNoQjtJQUVBdEMsUUFBUXNDLEdBQUcsQ0FBQztJQUNaLE1BQU0rQyxhQUFhO1FBQUM7UUFBVztRQUFZO0tBQVk7SUFDdkRBLFdBQVd4QixPQUFPLENBQUNsQyxDQUFBQTtRQUNmLE1BQU0yRSxTQUFTNUIsS0FBS2hFLElBQUksQ0FBQ3VCLENBQUFBLE9BQVFBLEtBQUtOLFFBQVEsS0FBS0E7UUFDbkQsSUFBSTJFLFFBQVE7Z0JBR1VBLHNCQUVOQSxnQkFDTUE7WUFMbEJ0RyxRQUFRc0MsR0FBRyxDQUFDLEdBQVksT0FBVFgsVUFBUyxNQUFJO2dCQUN4QmhDLElBQUkyRyxPQUFPM0csRUFBRTtnQkFDYjBCLFlBQVksR0FBRWlGLHVCQUFBQSxPQUFPakYsWUFBWSxjQUFuQmlGLDJDQUFBQSxxQkFBcUIxRyxLQUFLO2dCQUN4Q29CLFNBQVNzRixPQUFPdEYsT0FBTztnQkFDdkJ0QixNQUFNLEdBQUU0RyxpQkFBQUEsT0FBTzVHLE1BQU0sY0FBYjRHLHFDQUFBQSxlQUFlMUcsS0FBSztnQkFDNUI4RCxjQUFjNEMsRUFBQUEsa0JBQUFBLE9BQU9uRyxPQUFPLGNBQWRtRyxzQ0FBQUEsZ0JBQWdCOUQsTUFBTSxLQUFJO1lBQzVDO1FBQ0o7SUFDSjtJQUNBeEMsUUFBUTZILFFBQVE7QUFDcEIsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL2NyZXctdHJhaW5pbmcvdXRpbHMvY3Jldy10cmFpbmluZy11dGlscy50cz8zZWMyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEdldFRyYWluaW5nU2Vzc2lvblN0YXR1cyB9IGZyb20gJy4uLy4uLy4uL2xpYi9hY3Rpb25zJ1xuXG4vKipcbiAqIFVuaWZpZWQgdHJhaW5pbmcgZGF0YSBpbnRlcmZhY2UgdGhhdCBjb21iaW5lcyBvdmVyZHVlLCB1cGNvbWluZywgYW5kIGNvbXBsZXRlZCB0cmFpbmluZyBkYXRhXG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgVW5pZmllZFRyYWluaW5nRGF0YSB7XG4gICAgaWQ6IG51bWJlclxuICAgIGR1ZURhdGU6IHN0cmluZyAvLyBGb3Igb3ZlcmR1ZS91cGNvbWluZyB0aGlzIGlzIHRoZSBkdWUgZGF0ZSwgZm9yIGNvbXBsZXRlZCB0aGlzIGlzIHRoZSBjb21wbGV0aW9uIGRhdGVcbiAgICB2ZXNzZWxJRDogbnVtYmVyXG4gICAgdmVzc2VsOiB7XG4gICAgICAgIGlkOiBudW1iZXJcbiAgICAgICAgdGl0bGU6IHN0cmluZ1xuICAgICAgICBba2V5OiBzdHJpbmddOiBhbnkgLy8gQWxsb3cgZm9yIGFkZGl0aW9uYWwgdmVzc2VsIHByb3BlcnRpZXMgbGlrZSBwb3NpdGlvbiwgZXRjLlxuICAgIH1cbiAgICB0cmFpbmluZ1R5cGVJRDogbnVtYmVyXG4gICAgdHJhaW5pbmdUeXBlOiB7XG4gICAgICAgIGlkOiBudW1iZXJcbiAgICAgICAgdGl0bGU6IHN0cmluZ1xuICAgIH1cbiAgICBtZW1iZXJzOiBBcnJheTx7XG4gICAgICAgIGlkOiBudW1iZXJcbiAgICAgICAgZmlyc3ROYW1lPzogc3RyaW5nXG4gICAgICAgIHN1cm5hbWU/OiBzdHJpbmdcbiAgICB9PlxuICAgIHN0YXR1czoge1xuICAgICAgICBjbGFzczogc3RyaW5nXG4gICAgICAgIGxhYmVsOiBzdHJpbmdcbiAgICAgICAgaXNPdmVyZHVlOiBib29sZWFuXG4gICAgICAgIGR1ZVdpdGhpblNldmVuRGF5czogYm9vbGVhblxuICAgIH1cbiAgICBjYXRlZ29yeTogJ292ZXJkdWUnIHwgJ3VwY29taW5nJyB8ICdjb21wbGV0ZWQnIC8vIEFkZGVkIHRvIGhlbHAgd2l0aCBzb3J0aW5nIGFuZCBkaXNwbGF5XG4gICAgb3JpZ2luYWxEYXRhPzogYW55IC8vIFN0b3JlIG9yaWdpbmFsIGRhdGEgZm9yIHJlZmVyZW5jZSBpZiBuZWVkZWRcbn1cblxuLyoqXG4gKiBUcmFpbmluZyBwcmlvcml0eSBsZXZlbHMgZm9yIHNvcnRpbmdcbiAqL1xuZXhwb3J0IGVudW0gVHJhaW5pbmdQcmlvcml0eSB7XG4gICAgT1ZFUkRVRSA9IDEsXG4gICAgVVBDT01JTkcgPSAyLFxuICAgIENPTVBMRVRFRCA9IDNcbn1cblxuLyoqXG4gKiBUcmFuc2Zvcm0gY29tcGxldGVkIHRyYWluaW5nIHNlc3Npb25zIHRvIG1hdGNoIHRoZSB1bmlmaWVkIHRyYWluaW5nIGRhdGEgZm9ybWF0XG4gKiBPcHRpbWl6ZWQgdmVyc2lvbiB3aXRoIGJldHRlciBlcnJvciBoYW5kbGluZyBhbmQgcGVyZm9ybWFuY2VcbiAqIEBwYXJhbSB0cmFpbmluZ0xpc3QgLSBBcnJheSBvZiBjb21wbGV0ZWQgdHJhaW5pbmcgc2Vzc2lvbnNcbiAqIEBwYXJhbSBnZXRWZXNzZWxXaXRoSWNvbiAtIEZ1bmN0aW9uIHRvIGdldCBjb21wbGV0ZSB2ZXNzZWwgZGF0YSB3aXRoIHBvc2l0aW9uL2ljb25cbiAqIEByZXR1cm5zIEFycmF5IG9mIHRyYW5zZm9ybWVkIHRyYWluaW5nIGRhdGFcbiAqL1xuZXhwb3J0IGNvbnN0IHRyYW5zZm9ybUNvbXBsZXRlZFRyYWluaW5nVG9VbmlmaWVkRm9ybWF0ID0gKFxuICAgIHRyYWluaW5nTGlzdDogYW55W10sXG4gICAgZ2V0VmVzc2VsV2l0aEljb24/OiAoaWQ6IGFueSwgdmVzc2VsOiBhbnkpID0+IGFueVxuKTogVW5pZmllZFRyYWluaW5nRGF0YVtdID0+IHtcbiAgICBpZiAoIXRyYWluaW5nTGlzdCB8fCAhQXJyYXkuaXNBcnJheSh0cmFpbmluZ0xpc3QpKSB7XG4gICAgICAgIHJldHVybiBbXVxuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiB0cmFpbmluZ0xpc3QubWFwKCh0cmFpbmluZzogYW55KSA9PiB7XG4gICAgICAgIC8vIEVuaGFuY2VkIHZlc3NlbCBkYXRhIHRyYW5zZm9ybWF0aW9uIHdpdGggcG9zaXRpb24gaW5mb3JtYXRpb25cbiAgICAgICAgbGV0IGNvbXBsZXRlVmVzc2VsRGF0YSA9IHRyYWluaW5nLnZlc3NlbCB8fCB7IGlkOiAwLCB0aXRsZTogJ1Vua25vd24nIH1cblxuICAgICAgICBpZiAoZ2V0VmVzc2VsV2l0aEljb24gJiYgdHJhaW5pbmcudmVzc2VsPy5pZCkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAvLyBHZXQgY29tcGxldGUgdmVzc2VsIGRhdGEgaW5jbHVkaW5nIHBvc2l0aW9uLCBpY29uLCBhbmQgb3RoZXIgbWV0YWRhdGFcbiAgICAgICAgICAgICAgICBjb21wbGV0ZVZlc3NlbERhdGEgPSBnZXRWZXNzZWxXaXRoSWNvbih0cmFpbmluZy52ZXNzZWwuaWQsIHRyYWluaW5nLnZlc3NlbClcblxuICAgICAgICAgICAgICAgIC8vIEVuc3VyZSB3ZSBwcmVzZXJ2ZSBvcmlnaW5hbCB2ZXNzZWwgZGF0YSBpZiB0cmFuc2Zvcm1hdGlvbiBmYWlsc1xuICAgICAgICAgICAgICAgIGlmICghY29tcGxldGVWZXNzZWxEYXRhIHx8IHR5cGVvZiBjb21wbGV0ZVZlc3NlbERhdGEgIT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbXBsZXRlVmVzc2VsRGF0YSA9IHRyYWluaW5nLnZlc3NlbFxuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC8vIEFkZCBwb3NpdGlvbiBpbmZvcm1hdGlvbiBpZiBhdmFpbGFibGVcbiAgICAgICAgICAgICAgICBpZiAodHJhaW5pbmcudmVzc2VsLnBvc2l0aW9uICYmICFjb21wbGV0ZVZlc3NlbERhdGEucG9zaXRpb24pIHtcbiAgICAgICAgICAgICAgICAgICAgY29tcGxldGVWZXNzZWxEYXRhLnBvc2l0aW9uID0gdHJhaW5pbmcudmVzc2VsLnBvc2l0aW9uXG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgLy8gQWRkIGxvY2F0aW9uIHR5cGUgaWYgYXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgaWYgKHRyYWluaW5nLnRyYWluaW5nTG9jYXRpb25UeXBlICYmICFjb21wbGV0ZVZlc3NlbERhdGEudHJhaW5pbmdMb2NhdGlvblR5cGUpIHtcbiAgICAgICAgICAgICAgICAgICAgY29tcGxldGVWZXNzZWxEYXRhLnRyYWluaW5nTG9jYXRpb25UeXBlID0gdHJhaW5pbmcudHJhaW5pbmdMb2NhdGlvblR5cGVcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybignRmFpbGVkIHRvIGVuaGFuY2UgdmVzc2VsIGRhdGEgZm9yIHRyYWluaW5nOicsIHRyYWluaW5nLmlkLCBlcnJvcilcbiAgICAgICAgICAgICAgICBjb21wbGV0ZVZlc3NlbERhdGEgPSB0cmFpbmluZy52ZXNzZWxcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIC8vIEVuaGFuY2VkIG1lbWJlciBkZWR1cGxpY2F0aW9uIGFuZCBub3JtYWxpemF0aW9uXG4gICAgICAgIGNvbnN0IHJhd01lbWJlcnMgPSB0cmFpbmluZy5tZW1iZXJzPy5ub2RlcyB8fCBbXVxuICAgICAgICBjb25zdCBkZWR1cGxpY2F0ZWRNZW1iZXJzID0gcmF3TWVtYmVycy5yZWR1Y2UoKGFjYzogYW55W10sIG1lbWJlcjogYW55KSA9PiB7XG4gICAgICAgICAgICAvLyBDaGVjayBpZiBtZW1iZXIgYWxyZWFkeSBleGlzdHMgaW4gdGhlIGFjY3VtdWxhdG9yXG4gICAgICAgICAgICBjb25zdCBleGlzdGluZ01lbWJlciA9IGFjYy5maW5kKG0gPT4gbS5pZCA9PT0gbWVtYmVyLmlkKVxuXG4gICAgICAgICAgICBpZiAoZXhpc3RpbmdNZW1iZXIpIHtcbiAgICAgICAgICAgICAgICAvLyBVcGRhdGUgZXhpc3RpbmcgbWVtYmVyIHdpdGggbW9yZSBjb21wbGV0ZSBkYXRhXG4gICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIuZmlyc3ROYW1lID0gbWVtYmVyLmZpcnN0TmFtZSB8fCBleGlzdGluZ01lbWJlci5maXJzdE5hbWVcbiAgICAgICAgICAgICAgICBleGlzdGluZ01lbWJlci5zdXJuYW1lID0gbWVtYmVyLnN1cm5hbWUgfHwgZXhpc3RpbmdNZW1iZXIuc3VybmFtZVxuICAgICAgICAgICAgICAgIGV4aXN0aW5nTWVtYmVyLmVtYWlsID0gbWVtYmVyLmVtYWlsIHx8IGV4aXN0aW5nTWVtYmVyLmVtYWlsXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIEFkZCBuZXcgbWVtYmVyIHdpdGggbm9ybWFsaXplZCBkYXRhXG4gICAgICAgICAgICAgICAgYWNjLnB1c2goe1xuICAgICAgICAgICAgICAgICAgICBpZDogbWVtYmVyLmlkLFxuICAgICAgICAgICAgICAgICAgICBmaXJzdE5hbWU6IG1lbWJlci5maXJzdE5hbWUgfHwgJycsXG4gICAgICAgICAgICAgICAgICAgIHN1cm5hbWU6IG1lbWJlci5zdXJuYW1lIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICBlbWFpbDogbWVtYmVyLmVtYWlsIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICAuLi5tZW1iZXIgLy8gUHJlc2VydmUgYW55IGFkZGl0aW9uYWwgbWVtYmVyIGRhdGFcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIGFjY1xuICAgICAgICB9LCBbXSlcblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgaWQ6IHRyYWluaW5nLmlkLFxuICAgICAgICAgICAgZHVlRGF0ZTogdHJhaW5pbmcuZGF0ZSwgLy8gTWFwIGNvbXBsZXRpb24gZGF0ZSB0byBkdWVEYXRlIGZvciB1bmlmaWVkIHNvcnRpbmdcbiAgICAgICAgICAgIHZlc3NlbElEOiB0cmFpbmluZy52ZXNzZWw/LmlkIHx8IDAsXG4gICAgICAgICAgICB2ZXNzZWw6IGNvbXBsZXRlVmVzc2VsRGF0YSxcbiAgICAgICAgICAgIHRyYWluaW5nVHlwZUlEOiB0cmFpbmluZy50cmFpbmluZ1R5cGVzPy5ub2Rlcz8uWzBdPy5pZCB8fCAwLFxuICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiB0cmFpbmluZy50cmFpbmluZ1R5cGVzPy5ub2Rlcz8uWzBdIHx8IHsgaWQ6IDAsIHRpdGxlOiAnVW5rbm93bicgfSxcbiAgICAgICAgICAgIG1lbWJlcnM6IGRlZHVwbGljYXRlZE1lbWJlcnMsXG4gICAgICAgICAgICBzdGF0dXM6IHtcbiAgICAgICAgICAgICAgICBsYWJlbDogJ0NvbXBsZXRlZCcsXG4gICAgICAgICAgICAgICAgaXNPdmVyZHVlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICBjbGFzczogJ2JvcmRlciByb3VuZGVkIGJvcmRlci1ib3JkZXIgdGV4dC1pbnB1dCBiZy1vdXRlci1zcGFjZS01MCBwLTIgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyJyxcbiAgICAgICAgICAgICAgICBkdWVXaXRoaW5TZXZlbkRheXM6IGZhbHNlLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGNhdGVnb3J5OiAnY29tcGxldGVkJyBhcyBjb25zdCxcbiAgICAgICAgICAgIG9yaWdpbmFsRGF0YTogdHJhaW5pbmdcbiAgICAgICAgfVxuICAgICAgICB9KVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHRyYW5zZm9ybWluZyBjb21wbGV0ZWQgdHJhaW5pbmcgZGF0YTonLCBlcnJvcilcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgfVxufVxuXG4vKipcbiAqIFRyYW5zZm9ybSB0cmFpbmluZyBzZXNzaW9uIGR1ZXMgdG8gdW5pZmllZCBmb3JtYXQgd2l0aCBjYWxjdWxhdGVkIHN0YXR1c1xuICogQXBwbGllcyB0aGUgc2FtZSBncm91cGluZyBsb2dpYyBhcyBDcmV3VHJhaW5pbmdMaXN0XG4gKiBAcGFyYW0gdHJhaW5pbmdTZXNzaW9uRHVlcyAtIEFycmF5IG9mIHRyYWluaW5nIHNlc3Npb24gZHVlcyAob3ZlcmR1ZS91cGNvbWluZylcbiAqIEByZXR1cm5zIEFycmF5IG9mIHRyYW5zZm9ybWVkIHRyYWluaW5nIGRhdGEgd2l0aCBjYWxjdWxhdGVkIHN0YXR1c1xuICovXG5leHBvcnQgY29uc3QgdHJhbnNmb3JtVHJhaW5pbmdTZXNzaW9uRHVlc1RvVW5pZmllZEZvcm1hdCA9IChcbiAgICB0cmFpbmluZ1Nlc3Npb25EdWVzOiBhbnlbXVxuKTogVW5pZmllZFRyYWluaW5nRGF0YVtdID0+IHtcbiAgICBpZiAoIXRyYWluaW5nU2Vzc2lvbkR1ZXMgfHwgIUFycmF5LmlzQXJyYXkodHJhaW5pbmdTZXNzaW9uRHVlcykpIHtcbiAgICAgICAgcmV0dXJuIFtdXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgICAgLy8gQXBwbHkgdGhlIHNhbWUgZmlsdGVyaW5nIGxvZ2ljIGFzIENyZXdUcmFpbmluZ0xpc3RcbiAgICAgICAgLy8gRmlsdGVyIG91dCBjcmV3IG1lbWJlcnMgd2hvIGFyZSBubyBsb25nZXIgYXNzaWduZWQgdG8gdGhlIHZlc3NlbFxuICAgICAgICAvLyBNYWtlIHRoZSBmaWx0ZXJpbmcgbW9yZSByb2J1c3QgYnkgY2hlY2tpbmcgZm9yIHZhbGlkIGRhdGEgc3RydWN0dXJlc1xuICAgICAgICBjb25zdCBmaWx0ZXJlZERhdGEgPSB0cmFpbmluZ1Nlc3Npb25EdWVzLmZpbHRlcigoaXRlbTogYW55KSA9PiB7XG4gICAgICAgICAgICAvLyBDaGVjayBpZiBpdGVtIGhhcyByZXF1aXJlZCBwcm9wZXJ0aWVzXG4gICAgICAgICAgICBpZiAoIWl0ZW0gfHwgIWl0ZW0ubWVtYmVySUQgfHwgIWl0ZW0udmVzc2VsSUQpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIFNraXBwaW5nIGludmFsaWQgaXRlbTonLCBpdGVtKVxuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAvLyBDaGVjayB2ZXNzZWwgbWVtYmVyIGFzc2lnbm1lbnQgLSBtYWtlIHRoaXMgbW9yZSBmbGV4aWJsZVxuICAgICAgICAgICAgY29uc3QgaGFzVmFsaWRWZXNzZWxNZW1iZXJzID0gaXRlbS52ZXNzZWw/LnNlYUxvZ3NNZW1iZXJzPy5ub2Rlcz8uc29tZSgobTogYW55KSA9PiB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG0gJiYgbS5pZCA9PT0gaXRlbS5tZW1iZXJJRFxuICAgICAgICAgICAgfSlcblxuICAgICAgICAgICAgLy8gSWYgdmVzc2VsIG1lbWJlciBjaGVjayBmYWlscywgc3RpbGwgaW5jbHVkZSB0aGUgaXRlbVxuICAgICAgICAgICAgaWYgKCFoYXNWYWxpZFZlc3NlbE1lbWJlcnMpIHtcbiAgICAgICAgICAgICAgICAvLyBGb3Igbm93LCBpbmNsdWRlIGFsbCBpdGVtcyB0byBwcmV2ZW50IGRhdGEgbG9zcyAtIHRoaXMgbWlnaHQgbmVlZCBhZGp1c3RtZW50IGJhc2VkIG9uIGJ1c2luZXNzIGxvZ2ljXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgcmV0dXJuIHRydWVcbiAgICAgICAgfSlcblxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gQWZ0ZXIgdmVzc2VsIG1lbWJlciBmaWx0ZXJpbmc6Jywge1xuICAgICAgICAgICAgb3JpZ2luYWxDb3VudDogdHJhaW5pbmdTZXNzaW9uRHVlcy5sZW5ndGgsXG4gICAgICAgICAgICBmaWx0ZXJlZENvdW50OiBmaWx0ZXJlZERhdGEubGVuZ3RoLFxuICAgICAgICAgICAgcmVtb3ZlZENvdW50OiB0cmFpbmluZ1Nlc3Npb25EdWVzLmxlbmd0aCAtIGZpbHRlcmVkRGF0YS5sZW5ndGhcbiAgICAgICAgfSlcblxuICAgICAgICAvLyBBZGQgc3RhdHVzIHRvIGVhY2ggcmVjb3JkIHdpdGggZXJyb3IgaGFuZGxpbmdcbiAgICAgICAgY29uc3QgZHVlV2l0aFN0YXR1cyA9IGZpbHRlcmVkRGF0YS5tYXAoKGR1ZTogYW55KSA9PiB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHN0YXR1cyA9IEdldFRyYWluaW5nU2Vzc2lvblN0YXR1cyhkdWUpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uZHVlLCBzdGF0dXMgfVxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBFcnJvciBjYWxjdWxhdGluZyBzdGF0dXMgZm9yOicsIGR1ZSwgZXJyb3IpXG4gICAgICAgICAgICAgICAgLy8gUmV0dXJuIHdpdGggZGVmYXVsdCBzdGF0dXMgdG8gcHJldmVudCBkYXRhIGxvc3NcbiAgICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgICAgICAuLi5kdWUsXG4gICAgICAgICAgICAgICAgICAgIHN0YXR1czoge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3M6ICdib3JkZXIgcm91bmRlZCBib3JkZXItYm9yZGVyIHRleHQtaW5wdXQgYmctb3V0ZXItc3BhY2UtNTAgcC0yIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogJ1Vua25vd24nLFxuICAgICAgICAgICAgICAgICAgICAgICAgaXNPdmVyZHVlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGR1ZVdpdGhpblNldmVuRGF5czogZmFsc2UsXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pXG5cbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIEFmdGVyIHN0YXR1cyBjYWxjdWxhdGlvbjonLCB7XG4gICAgICAgICAgICB0b3RhbFJlY29yZHM6IGR1ZVdpdGhTdGF0dXMubGVuZ3RoLFxuICAgICAgICAgICAgc3RhdHVzQnJlYWtkb3duOiBkdWVXaXRoU3RhdHVzLnJlZHVjZSgoYWNjOiBhbnksIGl0ZW06IGFueSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGtleSA9IGl0ZW0uc3RhdHVzLmlzT3ZlcmR1ZSA/ICdvdmVyZHVlJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtLnN0YXR1cy5kdWVXaXRoaW5TZXZlbkRheXMgPyAndXBjb21pbmcnIDogJ2Z1dHVyZSdcbiAgICAgICAgICAgICAgICBhY2Nba2V5XSA9IChhY2Nba2V5XSB8fCAwKSArIDFcbiAgICAgICAgICAgICAgICByZXR1cm4gYWNjXG4gICAgICAgICAgICB9LCB7fSlcbiAgICAgICAgfSlcblxuICAgICAgICAvLyBBcHBseSB0aGUgc2FtZSBncm91cGluZyBsb2dpYyBhcyBDcmV3VHJhaW5pbmdMaXN0XG4gICAgICAgIC8vIEdyb3VwIGJ5IHZlc3NlbC10cmFpbmluZ1R5cGUtZHVlRGF0ZSB3aXRoIGJldHRlciBlcnJvciBoYW5kbGluZ1xuICAgICAgICBjb25zdCBncm91cGVkRHVlcyA9IGR1ZVdpdGhTdGF0dXMucmVkdWNlKFxuICAgICAgICAgICAgKGFjYzogYW55LCBkdWU6IGFueSkgPT4ge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGtleSA9IGAke2R1ZS52ZXNzZWxJRCB8fCAwfS0ke2R1ZS50cmFpbmluZ1R5cGVJRCB8fCAwfS0ke2R1ZS5kdWVEYXRlIHx8ICd1bmtub3duJ31gXG4gICAgICAgICAgICAgICAgICAgIGlmICghYWNjW2tleV0pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFjY1trZXldID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBkdWUuaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsSUQ6IGR1ZS52ZXNzZWxJRCB8fCAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbDogZHVlLnZlc3NlbCB8fCB7IGlkOiAwLCB0aXRsZTogJ1Vua25vd24nIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IGR1ZS50cmFpbmluZ1R5cGVJRCB8fCAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZTogZHVlLnRyYWluaW5nVHlwZSB8fCB7IGlkOiAwLCB0aXRsZTogJ1Vua25vd24nIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVlRGF0ZTogZHVlLmR1ZURhdGUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBkdWUuc3RhdHVzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nTG9jYXRpb25UeXBlOiBkdWUudHJhaW5pbmdTZXNzaW9uPy50cmFpbmluZ0xvY2F0aW9uVHlwZSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJzOiBbXSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoZHVlLm1lbWJlcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgYWNjW2tleV0ubWVtYmVycy5wdXNoKGR1ZS5tZW1iZXIpXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFjY1xuICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIEVycm9yIGdyb3VwaW5nIGR1ZTonLCBkdWUsIGVycm9yKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYWNjXG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHt9LFxuICAgICAgICApXG5cbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIEFmdGVyIGdyb3VwaW5nIGJ5IHZlc3NlbC10cmFpbmluZ1R5cGUtZHVlRGF0ZTonLCB7XG4gICAgICAgICAgICBncm91cENvdW50OiBPYmplY3Qua2V5cyhncm91cGVkRHVlcykubGVuZ3RoLFxuICAgICAgICAgICAgZ3JvdXBLZXlzOiBPYmplY3Qua2V5cyhncm91cGVkRHVlcyksXG4gICAgICAgICAgICBzYW1wbGVHcm91cDogT2JqZWN0LnZhbHVlcyhncm91cGVkRHVlcylbMF1cbiAgICAgICAgfSlcblxuICAgICAgICAvLyBNZXJnZSBtZW1iZXJzIHdpdGhpbiBlYWNoIGdyb3VwIChzYW1lIGFzIENyZXdUcmFpbmluZ0xpc3QpIHdpdGggb3B0aW1pemF0aW9uXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBTdGFydGluZyBtZW1iZXIgbWVyZ2luZyBmb3IgZ3JvdXBzOicsIE9iamVjdC52YWx1ZXMoZ3JvdXBlZER1ZXMpLmxlbmd0aClcbiAgICAgICAgY29uc3QgbWVyZ2VkRHVlcyA9IE9iamVjdC52YWx1ZXMoZ3JvdXBlZER1ZXMpLm1hcCgoZ3JvdXA6IGFueSkgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIFByb2Nlc3NpbmcgZ3JvdXA6Jywge1xuICAgICAgICAgICAgICAgIGlkOiBncm91cC5pZCxcbiAgICAgICAgICAgICAgICB2ZXNzZWxJRDogZ3JvdXAudmVzc2VsSUQsXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IGdyb3VwLnRyYWluaW5nVHlwZUlELFxuICAgICAgICAgICAgICAgIG1lbWJlcnNDb3VudDogZ3JvdXAubWVtYmVycz8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgICAgICAgbWVtYmVyczogZ3JvdXAubWVtYmVyc1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC8vIFVzZSBNYXAgZm9yIGZhc3RlciBtZW1iZXIgZGVkdXBsaWNhdGlvblxuICAgICAgICAgICAgY29uc3QgbWVtYmVyTWFwID0gbmV3IE1hcCgpXG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBQcm9jZXNzaW5nIG1lbWJlcnMgZm9yIGdyb3VwOicsIGdyb3VwLm1lbWJlcnMpXG5cbiAgICAgICAgICAgIGdyb3VwLm1lbWJlcnMuZm9yRWFjaCgobWVtYmVyOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gUHJvY2Vzc2luZyBtZW1iZXI6JywgbWVtYmVyKVxuICAgICAgICAgICAgICAgIGlmIChtZW1iZXIgJiYgbWVtYmVyLmlkKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nTWVtYmVyID0gbWVtYmVyTWFwLmdldChtZW1iZXIuaWQpXG4gICAgICAgICAgICAgICAgICAgIGlmIChleGlzdGluZ01lbWJlcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVXBkYXRlIGV4aXN0aW5nIG1lbWJlciB3aXRoIG1vcmUgY29tcGxldGUgZGF0YVxuICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyTWFwLnNldChtZW1iZXIuaWQsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5leGlzdGluZ01lbWJlcixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaXJzdE5hbWU6IG1lbWJlci5maXJzdE5hbWUgfHwgZXhpc3RpbmdNZW1iZXIuZmlyc3ROYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN1cm5hbWU6IG1lbWJlci5zdXJuYW1lIHx8IGV4aXN0aW5nTWVtYmVyLnN1cm5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZW1haWw6IG1lbWJlci5lbWFpbCB8fCBleGlzdGluZ01lbWJlci5lbWFpbCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5tZW1iZXIgLy8gUHJlc2VydmUgYW55IGFkZGl0aW9uYWwgbWVtYmVyIGRhdGFcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBBZGQgbmV3IG1lbWJlciB3aXRoIG5vcm1hbGl6ZWQgZGF0YVxuICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyTWFwLnNldChtZW1iZXIuaWQsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZDogbWVtYmVyLmlkLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpcnN0TmFtZTogbWVtYmVyLmZpcnN0TmFtZSB8fCAnJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdXJuYW1lOiBtZW1iZXIuc3VybmFtZSB8fCAnJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbWFpbDogbWVtYmVyLmVtYWlsIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLm1lbWJlciAvLyBQcmVzZXJ2ZSBhbnkgYWRkaXRpb25hbCBtZW1iZXIgZGF0YVxuICAgICAgICAgICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybign8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gSW52YWxpZCBtZW1iZXI6JywgbWVtYmVyKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pXG5cbiAgICAgICAgICAgIGNvbnN0IG1lcmdlZE1lbWJlcnMgPSBBcnJheS5mcm9tKG1lbWJlck1hcC52YWx1ZXMoKSlcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBNZXJnZWQgbWVtYmVycyByZXN1bHQ6JywgbWVyZ2VkTWVtYmVycylcblxuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAvLyBEZXRlcm1pbmUgY2F0ZWdvcnkgYmFzZWQgb24gc3RhdHVzXG4gICAgICAgICAgICAgICAgbGV0IGNhdGVnb3J5OiAnb3ZlcmR1ZScgfCAndXBjb21pbmcnIHwgJ2NvbXBsZXRlZCdcbiAgICAgICAgICAgICAgICBpZiAoZ3JvdXAuc3RhdHVzPy5pc092ZXJkdWUpIHtcbiAgICAgICAgICAgICAgICAgICAgY2F0ZWdvcnkgPSAnb3ZlcmR1ZSdcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKGdyb3VwLnN0YXR1cz8uZHVlV2l0aGluU2V2ZW5EYXlzKSB7XG4gICAgICAgICAgICAgICAgICAgIGNhdGVnb3J5ID0gJ3VwY29taW5nJ1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGNhdGVnb3J5ID0gJ3VwY29taW5nJyAvLyBEZWZhdWx0IGZvciBmdXR1cmUgZHVlIGRhdGVzXG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIERldGVybWluZWQgY2F0ZWdvcnk6JywgY2F0ZWdvcnksICdmb3Igc3RhdHVzOicsIGdyb3VwLnN0YXR1cylcblxuICAgICAgICAgICAgICAgIC8vIEVuaGFuY2VkIHZlc3NlbCBkYXRhIHdpdGggcG9zaXRpb24gaW5mb3JtYXRpb25cbiAgICAgICAgICAgICAgICAvLyBDcmVhdGUgYSBuZXcgb2JqZWN0IHRvIGF2b2lkIFwib2JqZWN0IGlzIG5vdCBleHRlbnNpYmxlXCIgZXJyb3JzXG4gICAgICAgICAgICAgICAgY29uc3QgZW5oYW5jZWRWZXNzZWwgPSB7XG4gICAgICAgICAgICAgICAgICAgIC4uLihncm91cC52ZXNzZWwgfHwgeyBpZDogMCwgdGl0bGU6ICdVbmtub3duJyB9KSxcbiAgICAgICAgICAgICAgICAgICAgLy8gQWRkIHRyYWluaW5nIGxvY2F0aW9uIHR5cGUgaWYgYXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgICAgIC4uLihncm91cC50cmFpbmluZ0xvY2F0aW9uVHlwZSAmJiB7IHRyYWluaW5nTG9jYXRpb25UeXBlOiBncm91cC50cmFpbmluZ0xvY2F0aW9uVHlwZSB9KVxuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHtcbiAgICAgICAgICAgICAgICAgICAgaWQ6IGdyb3VwLmlkLFxuICAgICAgICAgICAgICAgICAgICBkdWVEYXRlOiBncm91cC5kdWVEYXRlLFxuICAgICAgICAgICAgICAgICAgICB2ZXNzZWxJRDogZ3JvdXAudmVzc2VsSUQsXG4gICAgICAgICAgICAgICAgICAgIHZlc3NlbDogZW5oYW5jZWRWZXNzZWwsXG4gICAgICAgICAgICAgICAgICAgIHRyYWluaW5nVHlwZUlEOiBncm91cC50cmFpbmluZ1R5cGVJRCxcbiAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiBncm91cC50cmFpbmluZ1R5cGUgfHwgeyBpZDogMCwgdGl0bGU6ICdVbmtub3duJyB9LFxuICAgICAgICAgICAgICAgICAgICBtZW1iZXJzOiBtZXJnZWRNZW1iZXJzLFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IGdyb3VwLnN0YXR1cyxcbiAgICAgICAgICAgICAgICAgICAgY2F0ZWdvcnksXG4gICAgICAgICAgICAgICAgICAgIG9yaWdpbmFsRGF0YTogZ3JvdXBcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gQ3JlYXRlZCB1bmlmaWVkIHJlY29yZDonLCByZXN1bHQpXG4gICAgICAgICAgICAgICAgcmV0dXJuIHJlc3VsdFxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBFcnJvciBjcmVhdGluZyB1bmlmaWVkIHJlY29yZDonLCBlcnJvciwgJ2ZvciBncm91cDonLCBncm91cClcbiAgICAgICAgICAgICAgICByZXR1cm4gbnVsbCAvLyBSZXR1cm4gbnVsbCB0byBmaWx0ZXIgb3V0IGZhaWxlZCByZWNvcmRzXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pLmZpbHRlcihCb29sZWFuKSBhcyBVbmlmaWVkVHJhaW5pbmdEYXRhW10gLy8gRmlsdGVyIG91dCBudWxsIHZhbHVlcyBhbmQgY2FzdFxuXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBGaW5hbCBtZXJnZWQgZHVlcyBkYXRhOicsIHtcbiAgICAgICAgICAgIHRvdGFsUmVjb3JkczogbWVyZ2VkRHVlcy5sZW5ndGgsXG4gICAgICAgICAgICBjYXRlZ29yeUJyZWFrZG93bjogbWVyZ2VkRHVlcy5yZWR1Y2UoKGFjYzogYW55LCBpdGVtOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICBhY2NbaXRlbS5jYXRlZ29yeV0gPSAoYWNjW2l0ZW0uY2F0ZWdvcnldIHx8IDApICsgMVxuICAgICAgICAgICAgICAgIHJldHVybiBhY2NcbiAgICAgICAgICAgIH0sIHt9KSxcbiAgICAgICAgICAgIHNhbXBsZVJlY29yZDogbWVyZ2VkRHVlc1swXSxcbiAgICAgICAgICAgIGFsbFJlY29yZHM6IG1lcmdlZER1ZXNcbiAgICAgICAgfSlcblxuICAgICAgICByZXR1cm4gbWVyZ2VkRHVlc1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIEVycm9yIGluIHRyYW5zZm9ybVRyYWluaW5nU2Vzc2lvbkR1ZXNUb1VuaWZpZWRGb3JtYXQ6JywgZXJyb3IpXG4gICAgICAgIHJldHVybiBbXVxuICAgIH1cbn1cblxuLyoqXG4gKiBHZXQgcHJpb3JpdHkgdmFsdWUgZm9yIHNvcnRpbmcgYmFzZWQgb24gdHJhaW5pbmcgY2F0ZWdvcnkgYW5kIHN0YXR1c1xuICogQHBhcmFtIHRyYWluaW5nIC0gVW5pZmllZCB0cmFpbmluZyBkYXRhIGl0ZW1cbiAqIEByZXR1cm5zIFByaW9yaXR5IG51bWJlciAobG93ZXIgPSBoaWdoZXIgcHJpb3JpdHkpXG4gKi9cbmNvbnN0IGdldFRyYWluaW5nUHJpb3JpdHkgPSAodHJhaW5pbmc6IFVuaWZpZWRUcmFpbmluZ0RhdGEpOiBudW1iZXIgPT4ge1xuICAgIHN3aXRjaCAodHJhaW5pbmcuY2F0ZWdvcnkpIHtcbiAgICAgICAgY2FzZSAnb3ZlcmR1ZSc6XG4gICAgICAgICAgICByZXR1cm4gVHJhaW5pbmdQcmlvcml0eS5PVkVSRFVFXG4gICAgICAgIGNhc2UgJ3VwY29taW5nJzpcbiAgICAgICAgICAgIHJldHVybiBUcmFpbmluZ1ByaW9yaXR5LlVQQ09NSU5HXG4gICAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6XG4gICAgICAgICAgICByZXR1cm4gVHJhaW5pbmdQcmlvcml0eS5DT01QTEVURURcbiAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgIHJldHVybiBUcmFpbmluZ1ByaW9yaXR5LkNPTVBMRVRFRFxuICAgIH1cbn1cblxuLyoqXG4gKiBSZW1vdmUgZHVwbGljYXRlIHRyYWluaW5nIHJlY29yZHMgYmFzZWQgb24gSUQgd2l0aCBlbmhhbmNlZCBkZWR1cGxpY2F0aW9uIGxvZ2ljXG4gKiBPcHRpbWl6ZWQgdmVyc2lvbiB1c2luZyBNYXAgZm9yIGJldHRlciBwZXJmb3JtYW5jZVxuICogQHBhcmFtIGRhdGEgLSBBcnJheSBvZiB1bmlmaWVkIHRyYWluaW5nIGRhdGFcbiAqIEByZXR1cm5zIERlZHVwbGljYXRlZCBhcnJheSB3aXRoIG1lcmdlZCBtZW1iZXIgZGF0YVxuICovXG5leHBvcnQgY29uc3QgZGVkdXBsaWNhdGVUcmFpbmluZ0RhdGEgPSAoZGF0YTogVW5pZmllZFRyYWluaW5nRGF0YVtdKTogVW5pZmllZFRyYWluaW5nRGF0YVtdID0+IHtcbiAgICBpZiAoIWRhdGEgfHwgIUFycmF5LmlzQXJyYXkoZGF0YSkpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIE5vIGRhdGEgcHJvdmlkZWQgZm9yIGRlZHVwbGljYXRpb24nKVxuICAgICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygn8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gU3RhcnRpbmcgZGVkdXBsaWNhdGlvbjonLCB7XG4gICAgICAgIHRvdGFsUmVjb3JkczogZGF0YS5sZW5ndGgsXG4gICAgICAgIHNhbXBsZVJlY29yZDogZGF0YVswXVxuICAgIH0pXG5cbiAgICB0cnkge1xuICAgICAgICAvLyBVc2UgTWFwIGZvciBPKDEpIGxvb2t1cHMgaW5zdGVhZCBvZiBPKG4pIGZpbmRJbmRleFxuICAgICAgICBjb25zdCByZWNvcmRNYXAgPSBuZXcgTWFwPG51bWJlciwgVW5pZmllZFRyYWluaW5nRGF0YT4oKVxuXG4gICAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBkYXRhKSB7XG4gICAgICAgICAgICAvLyBIYW5kbGUgYm90aCBzdHJpbmcgYW5kIG51bWJlciBJRHNcbiAgICAgICAgICAgIGNvbnN0IGl0ZW1JZCA9IHR5cGVvZiBpdGVtLmlkID09PSAnc3RyaW5nJyA/IHBhcnNlSW50KGl0ZW0uaWQsIDEwKSA6IGl0ZW0uaWRcblxuICAgICAgICAgICAgaWYgKCFpdGVtIHx8ICghaXRlbUlkICYmIGl0ZW1JZCAhPT0gMCkgfHwgaXNOYU4oaXRlbUlkKSkge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybign8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gU2tpcHBpbmcgaW52YWxpZCBpdGVtIGluIGRlZHVwbGljYXRpb246JywgaXRlbSlcbiAgICAgICAgICAgICAgICBjb250aW51ZVxuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAocmVjb3JkTWFwLmhhcyhpdGVtSWQpKSB7XG4gICAgICAgICAgICAgICAgLy8gTWVyZ2Ugd2l0aCBleGlzdGluZyByZWNvcmRcbiAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZyA9IHJlY29yZE1hcC5nZXQoaXRlbUlkKSFcblxuICAgICAgICAgICAgICAgIC8vIFVzZSBNYXAgZm9yIG1lbWJlciBkZWR1cGxpY2F0aW9uIGZvciBiZXR0ZXIgcGVyZm9ybWFuY2VcbiAgICAgICAgICAgICAgICBjb25zdCBtZW1iZXJNYXAgPSBuZXcgTWFwPG51bWJlciwgYW55PigpXG5cbiAgICAgICAgICAgICAgICAvLyBBZGQgZXhpc3RpbmcgbWVtYmVyc1xuICAgICAgICAgICAgICAgIGV4aXN0aW5nLm1lbWJlcnM/LmZvckVhY2gobWVtYmVyID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG1lbWJlciAmJiBtZW1iZXIuaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlck1hcC5zZXQobWVtYmVyLmlkLCBtZW1iZXIpXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KVxuXG4gICAgICAgICAgICAgICAgLy8gQWRkL21lcmdlIG5ldyBtZW1iZXJzXG4gICAgICAgICAgICAgICAgaXRlbS5tZW1iZXJzPy5mb3JFYWNoKG1lbWJlciA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChtZW1iZXIgJiYgbWVtYmVyLmlkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZ01lbWJlciA9IG1lbWJlck1hcC5nZXQobWVtYmVyLmlkKVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGV4aXN0aW5nTWVtYmVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gTWVyZ2UgbWVtYmVyIGRhdGEsIHByZWZlcnJpbmcgbm9uLWVtcHR5IHZhbHVlc1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlck1hcC5zZXQobWVtYmVyLmlkLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC4uLmV4aXN0aW5nTWVtYmVyLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaXJzdE5hbWU6IG1lbWJlci5maXJzdE5hbWUgfHwgZXhpc3RpbmdNZW1iZXIuZmlyc3ROYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdXJuYW1lOiBtZW1iZXIuc3VybmFtZSB8fCBleGlzdGluZ01lbWJlci5zdXJuYW1lLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlbWFpbDogKG1lbWJlciBhcyBhbnkpLmVtYWlsIHx8IChleGlzdGluZ01lbWJlciBhcyBhbnkpLmVtYWlsLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5tZW1iZXIgLy8gUHJlc2VydmUgYW55IGFkZGl0aW9uYWwgbWVtYmVyIGRhdGFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXJNYXAuc2V0KG1lbWJlci5pZCwgbWVtYmVyKVxuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSlcblxuICAgICAgICAgICAgICAgIC8vIFVwZGF0ZSB0aGUgZXhpc3RpbmcgcmVjb3JkIHdpdGggbWVyZ2VkIGRhdGFcbiAgICAgICAgICAgICAgICByZWNvcmRNYXAuc2V0KGl0ZW1JZCwge1xuICAgICAgICAgICAgICAgICAgICAuLi5leGlzdGluZyxcbiAgICAgICAgICAgICAgICAgICAgbWVtYmVyczogQXJyYXkuZnJvbShtZW1iZXJNYXAudmFsdWVzKCkpLFxuICAgICAgICAgICAgICAgICAgICAvLyBQcmVmZXIgbW9yZSByZWNlbnQgb3IgY29tcGxldGUgZGF0YVxuICAgICAgICAgICAgICAgICAgICB2ZXNzZWw6IChpdGVtLnZlc3NlbD8udGl0bGUgJiYgaXRlbS52ZXNzZWwudGl0bGUgIT09ICdVbmtub3duJykgPyBpdGVtLnZlc3NlbCA6IGV4aXN0aW5nLnZlc3NlbCxcbiAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiAoaXRlbS50cmFpbmluZ1R5cGU/LnRpdGxlICYmIGl0ZW0udHJhaW5pbmdUeXBlLnRpdGxlICE9PSAnVW5rbm93bicpID8gaXRlbS50cmFpbmluZ1R5cGUgOiBleGlzdGluZy50cmFpbmluZ1R5cGUsXG4gICAgICAgICAgICAgICAgICAgIC8vIFByZWZlciBvdmVyZHVlIHN0YXR1cyBvdmVyIHVwY29taW5nL2NvbXBsZXRlZFxuICAgICAgICAgICAgICAgICAgICBzdGF0dXM6IGl0ZW0uc3RhdHVzPy5pc092ZXJkdWUgPyBpdGVtLnN0YXR1cyA6IGV4aXN0aW5nLnN0YXR1c1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIEFkZCBuZXcgcmVjb3JkXG4gICAgICAgICAgICAgICAgcmVjb3JkTWFwLnNldChpdGVtLmlkLCBpdGVtKVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgZGVkdXBsaWNhdGVkRGF0YSA9IEFycmF5LmZyb20ocmVjb3JkTWFwLnZhbHVlcygpKVxuXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBEZWR1cGxpY2F0aW9uIGNvbXBsZXRlOicsIHtcbiAgICAgICAgICAgIG9yaWdpbmFsQ291bnQ6IGRhdGEubGVuZ3RoLFxuICAgICAgICAgICAgZGVkdXBsaWNhdGVkQ291bnQ6IGRlZHVwbGljYXRlZERhdGEubGVuZ3RoLFxuICAgICAgICAgICAgcmVtb3ZlZER1cGxpY2F0ZXM6IGRhdGEubGVuZ3RoIC0gZGVkdXBsaWNhdGVkRGF0YS5sZW5ndGgsXG4gICAgICAgICAgICBjYXRlZ29yeUJyZWFrZG93bjogZGVkdXBsaWNhdGVkRGF0YS5yZWR1Y2UoKGFjYzogYW55LCBpdGVtOiBhbnkpID0+IHtcbiAgICAgICAgICAgICAgICBhY2NbaXRlbS5jYXRlZ29yeV0gPSAoYWNjW2l0ZW0uY2F0ZWdvcnldIHx8IDApICsgMVxuICAgICAgICAgICAgICAgIHJldHVybiBhY2NcbiAgICAgICAgICAgIH0sIHt9KVxuICAgICAgICB9KVxuXG4gICAgICAgIHJldHVybiBkZWR1cGxpY2F0ZWREYXRhXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gRXJyb3IgaW4gZGVkdXBsaWNhdGlvbjonLCBlcnJvcilcbiAgICAgICAgcmV0dXJuIGRhdGEgLy8gUmV0dXJuIG9yaWdpbmFsIGRhdGEgaWYgZGVkdXBsaWNhdGlvbiBmYWlsc1xuICAgIH1cbn1cblxuLyoqXG4gKiBTb3J0IHVuaWZpZWQgdHJhaW5pbmcgZGF0YSB3aXRoIHByaW9yaXR5LWJhc2VkIG9yZGVyaW5nXG4gKiBPcHRpbWl6ZWQgdmVyc2lvbiB3aXRoIGJldHRlciBwZXJmb3JtYW5jZSBhbmQgZXJyb3IgaGFuZGxpbmdcbiAqIEBwYXJhbSBkYXRhIC0gQXJyYXkgb2YgdW5pZmllZCB0cmFpbmluZyBkYXRhXG4gKiBAcmV0dXJucyBTb3J0ZWQgYXJyYXkgd2l0aCBvdmVyZHVlIGZpcnN0LCB0aGVuIHVwY29taW5nLCB0aGVuIGNvbXBsZXRlZFxuICovXG5leHBvcnQgY29uc3Qgc29ydFVuaWZpZWRUcmFpbmluZ0RhdGEgPSAoZGF0YTogVW5pZmllZFRyYWluaW5nRGF0YVtdKTogVW5pZmllZFRyYWluaW5nRGF0YVtdID0+IHtcbiAgICBpZiAoIWRhdGEgfHwgIUFycmF5LmlzQXJyYXkoZGF0YSkpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIE5vIGRhdGEgcHJvdmlkZWQgZm9yIHNvcnRpbmcnKVxuICAgICAgICByZXR1cm4gW11cbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZygn8J+UjSBbY3Jldy10cmFpbmluZy11dGlsc10gU3RhcnRpbmcgc29ydDonLCB7XG4gICAgICAgIHRvdGFsUmVjb3JkczogZGF0YS5sZW5ndGgsXG4gICAgICAgIGNhdGVnb3JpZXM6IGRhdGEucmVkdWNlKChhY2M6IGFueSwgaXRlbTogYW55KSA9PiB7XG4gICAgICAgICAgICBhY2NbaXRlbS5jYXRlZ29yeV0gPSAoYWNjW2l0ZW0uY2F0ZWdvcnldIHx8IDApICsgMVxuICAgICAgICAgICAgcmV0dXJuIGFjY1xuICAgICAgICB9LCB7fSlcbiAgICB9KVxuXG4gICAgdHJ5IHtcbiAgICAgICAgcmV0dXJuIGRhdGEuc29ydCgoYSwgYikgPT4ge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAvLyBGaXJzdCBzb3J0IGJ5IHByaW9yaXR5IChvdmVyZHVlID4gdXBjb21pbmcgPiBjb21wbGV0ZWQpXG4gICAgICAgICAgICAgICAgY29uc3QgcHJpb3JpdHlEaWZmID0gZ2V0VHJhaW5pbmdQcmlvcml0eShhKSAtIGdldFRyYWluaW5nUHJpb3JpdHkoYilcbiAgICAgICAgICAgICAgICBpZiAocHJpb3JpdHlEaWZmICE9PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBwcmlvcml0eURpZmZcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAvLyBXaXRoaW4gc2FtZSBwcmlvcml0eSwgc29ydCBieSBkYXRlIHdpdGggZXJyb3IgaGFuZGxpbmdcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRlQSA9IGEuZHVlRGF0ZSA/IG5ldyBEYXRlKGEuZHVlRGF0ZSkuZ2V0VGltZSgpIDogMFxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGVCID0gYi5kdWVEYXRlID8gbmV3IERhdGUoYi5kdWVEYXRlKS5nZXRUaW1lKCkgOiAwXG5cbiAgICAgICAgICAgICAgICAvLyBIYW5kbGUgaW52YWxpZCBkYXRlc1xuICAgICAgICAgICAgICAgIGlmIChpc05hTihkYXRlQSkgJiYgaXNOYU4oZGF0ZUIpKSByZXR1cm4gMFxuICAgICAgICAgICAgICAgIGlmIChpc05hTihkYXRlQSkpIHJldHVybiAxXG4gICAgICAgICAgICAgICAgaWYgKGlzTmFOKGRhdGVCKSkgcmV0dXJuIC0xXG5cbiAgICAgICAgICAgICAgICBpZiAoYS5jYXRlZ29yeSA9PT0gJ292ZXJkdWUnKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEZvciBvdmVyZHVlOiBtb3N0IG92ZXJkdWUgZmlyc3QgKGVhcmxpZXN0IGR1ZSBkYXRlIGZpcnN0KVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZGF0ZUEgLSBkYXRlQlxuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoYS5jYXRlZ29yeSA9PT0gJ3VwY29taW5nJykge1xuICAgICAgICAgICAgICAgICAgICAvLyBGb3IgdXBjb21pbmc6IHNvb25lc3QgZHVlIGRhdGUgZmlyc3RcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGVBIC0gZGF0ZUJcbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyBGb3IgY29tcGxldGVkOiBtb3N0IHJlY2VudCBjb21wbGV0aW9uIGZpcnN0IChsYXRlc3QgZGF0ZSBmaXJzdClcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGVCIC0gZGF0ZUFcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflI0gW2NyZXctdHJhaW5pbmctdXRpbHNdIEVycm9yIGNvbXBhcmluZyBpdGVtcyBpbiBzb3J0OicsIGVycm9yLCB7IGEsIGIgfSlcbiAgICAgICAgICAgICAgICByZXR1cm4gMCAvLyBLZWVwIG9yaWdpbmFsIG9yZGVyIGlmIGNvbXBhcmlzb24gZmFpbHNcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SNIFtjcmV3LXRyYWluaW5nLXV0aWxzXSBFcnJvciBpbiBzb3J0aW5nOicsIGVycm9yKVxuICAgICAgICByZXR1cm4gZGF0YSAvLyBSZXR1cm4gb3JpZ2luYWwgZGF0YSBpZiBzb3J0aW5nIGZhaWxzXG4gICAgfVxufVxuXG4vKipcbiAqIE1haW4gZnVuY3Rpb24gdG8gbWVyZ2UgYW5kIHNvcnQgY3JldyB0cmFpbmluZyBkYXRhIGZyb20gbXVsdGlwbGUgc291cmNlc1xuICogT3B0aW1pemVkIHZlcnNpb24gd2l0aCBjb21wcmVoZW5zaXZlIGRlYnVnZ2luZyBhbmQgZXJyb3IgaGFuZGxpbmdcbiAqIEBwYXJhbSBvcHRpb25zIC0gQ29uZmlndXJhdGlvbiBvYmplY3Qgd2l0aCBkYXRhIHNvdXJjZXMgYW5kIHV0aWxpdGllc1xuICogQHJldHVybnMgVW5pZmllZCBhbmQgc29ydGVkIHRyYWluaW5nIGRhdGEgYXJyYXlcbiAqL1xuZXhwb3J0IGNvbnN0IG1lcmdlQW5kU29ydENyZXdUcmFpbmluZ0RhdGEgPSAoe1xuICAgIHRyYWluaW5nU2Vzc2lvbkR1ZXMgPSBbXSxcbiAgICBjb21wbGV0ZWRUcmFpbmluZ0xpc3QgPSBbXSxcbiAgICBnZXRWZXNzZWxXaXRoSWNvbixcbiAgICBpbmNsdWRlQ29tcGxldGVkID0gdHJ1ZSxcbiAgICBkZWJ1ZyA9IGZhbHNlXG59OiB7XG4gICAgdHJhaW5pbmdTZXNzaW9uRHVlcz86IGFueVtdXG4gICAgY29tcGxldGVkVHJhaW5pbmdMaXN0PzogYW55W11cbiAgICBnZXRWZXNzZWxXaXRoSWNvbj86IChpZDogYW55LCB2ZXNzZWw6IGFueSkgPT4gYW55XG4gICAgaW5jbHVkZUNvbXBsZXRlZD86IGJvb2xlYW5cbiAgICBkZWJ1Zz86IGJvb2xlYW5cbn0pOiBVbmlmaWVkVHJhaW5pbmdEYXRhW10gPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5SNIFttZXJnZUFuZFNvcnRDcmV3VHJhaW5pbmdEYXRhXSBTdGFydGluZyBtZXJnZSBwcm9jZXNzOicsIHtcbiAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlc0NvdW50OiB0cmFpbmluZ1Nlc3Npb25EdWVzPy5sZW5ndGggfHwgMCxcbiAgICAgICAgY29tcGxldGVkVHJhaW5pbmdMaXN0Q291bnQ6IGNvbXBsZXRlZFRyYWluaW5nTGlzdD8ubGVuZ3RoIHx8IDAsXG4gICAgICAgIGluY2x1ZGVDb21wbGV0ZWQsXG4gICAgICAgIGRlYnVnXG4gICAgfSlcblxuICAgIHRyeSB7XG4gICAgICAgIC8vIFRyYW5zZm9ybSBvdmVyZHVlL3VwY29taW5nIHRyYWluaW5nIGRhdGFcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW21lcmdlQW5kU29ydENyZXdUcmFpbmluZ0RhdGFdIFRyYW5zZm9ybWluZyB0cmFpbmluZyBzZXNzaW9uIGR1ZXMuLi4nKVxuICAgICAgICBjb25zdCB0cmFuc2Zvcm1lZER1ZXMgPSB0cmFuc2Zvcm1UcmFpbmluZ1Nlc3Npb25EdWVzVG9VbmlmaWVkRm9ybWF0KHRyYWluaW5nU2Vzc2lvbkR1ZXMpXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFttZXJnZUFuZFNvcnRDcmV3VHJhaW5pbmdEYXRhXSBUcmFuc2Zvcm1lZCBkdWVzIHJlc3VsdDonLCB7XG4gICAgICAgICAgICBjb3VudDogdHJhbnNmb3JtZWREdWVzLmxlbmd0aCxcbiAgICAgICAgICAgIHNhbXBsZTogdHJhbnNmb3JtZWREdWVzWzBdXG4gICAgICAgIH0pXG5cbiAgICAgICAgLy8gVHJhbnNmb3JtIGNvbXBsZXRlZCB0cmFpbmluZyBkYXRhIGlmIHJlcXVlc3RlZFxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbbWVyZ2VBbmRTb3J0Q3Jld1RyYWluaW5nRGF0YV0gVHJhbnNmb3JtaW5nIGNvbXBsZXRlZCB0cmFpbmluZy4uLicpXG4gICAgICAgIGNvbnN0IHRyYW5zZm9ybWVkQ29tcGxldGVkID0gaW5jbHVkZUNvbXBsZXRlZFxuICAgICAgICAgICAgPyB0cmFuc2Zvcm1Db21wbGV0ZWRUcmFpbmluZ1RvVW5pZmllZEZvcm1hdChjb21wbGV0ZWRUcmFpbmluZ0xpc3QsIGdldFZlc3NlbFdpdGhJY29uKVxuICAgICAgICAgICAgOiBbXVxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbbWVyZ2VBbmRTb3J0Q3Jld1RyYWluaW5nRGF0YV0gVHJhbnNmb3JtZWQgY29tcGxldGVkIHJlc3VsdDonLCB7XG4gICAgICAgICAgICBjb3VudDogdHJhbnNmb3JtZWRDb21wbGV0ZWQubGVuZ3RoLFxuICAgICAgICAgICAgc2FtcGxlOiB0cmFuc2Zvcm1lZENvbXBsZXRlZFswXVxuICAgICAgICB9KVxuXG4gICAgICAgIC8vIENvbWJpbmUgYWxsIGRhdGFcbiAgICAgICAgY29uc3QgY29tYmluZWREYXRhID0gWy4uLnRyYW5zZm9ybWVkRHVlcywgLi4udHJhbnNmb3JtZWRDb21wbGV0ZWRdXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIFttZXJnZUFuZFNvcnRDcmV3VHJhaW5pbmdEYXRhXSBDb21iaW5lZCBkYXRhOicsIHtcbiAgICAgICAgICAgIHRvdGFsQ291bnQ6IGNvbWJpbmVkRGF0YS5sZW5ndGgsXG4gICAgICAgICAgICBkdWVzQ291bnQ6IHRyYW5zZm9ybWVkRHVlcy5sZW5ndGgsXG4gICAgICAgICAgICBjb21wbGV0ZWRDb3VudDogdHJhbnNmb3JtZWRDb21wbGV0ZWQubGVuZ3RoLFxuICAgICAgICAgICAgc2FtcGxlOiBjb21iaW5lZERhdGFbMF1cbiAgICAgICAgfSlcblxuICAgICAgICAvLyBTb3J0IHdpdGggcHJpb3JpdHktYmFzZWQgb3JkZXJpbmcgKGRlZHVwbGljYXRpb24gcmVtb3ZlZClcbiAgICAgICAgY29uc29sZS5sb2coJ/CflI0gW21lcmdlQW5kU29ydENyZXdUcmFpbmluZ0RhdGFdIFN0YXJ0aW5nIHNvcnRpbmcuLi4nKVxuICAgICAgICBjb25zdCBzb3J0ZWREYXRhID0gc29ydFVuaWZpZWRUcmFpbmluZ0RhdGEoY29tYmluZWREYXRhKVxuICAgICAgICBjb25zb2xlLmxvZygn8J+UjSBbbWVyZ2VBbmRTb3J0Q3Jld1RyYWluaW5nRGF0YV0gRmluYWwgcmVzdWx0OicsIHtcbiAgICAgICAgICAgIHRvdGFsQ291bnQ6IHNvcnRlZERhdGEubGVuZ3RoLFxuICAgICAgICAgICAgY2F0ZWdvcnlCcmVha2Rvd246IHNvcnRlZERhdGEucmVkdWNlKChhY2M6IGFueSwgaXRlbTogYW55KSA9PiB7XG4gICAgICAgICAgICAgICAgYWNjW2l0ZW0uY2F0ZWdvcnldID0gKGFjY1tpdGVtLmNhdGVnb3J5XSB8fCAwKSArIDFcbiAgICAgICAgICAgICAgICByZXR1cm4gYWNjXG4gICAgICAgICAgICB9LCB7fSksXG4gICAgICAgICAgICBzYW1wbGU6IHNvcnRlZERhdGFbMF1cbiAgICAgICAgfSlcblxuICAgICAgICAvLyBPcHRpb25hbCBkZWJ1ZyBhbmFseXNpc1xuICAgICAgICBpZiAoZGVidWcpIHtcbiAgICAgICAgICAgIGRlYnVnVHJhaW5pbmdEYXRhKHNvcnRlZERhdGEsICdGaW5hbCBNZXJnZWQgVHJhaW5pbmcgRGF0YScpXG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gc29ydGVkRGF0YVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBtZXJnaW5nIGFuZCBzb3J0aW5nIGNyZXcgdHJhaW5pbmcgZGF0YTonLCBlcnJvcilcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIHN0YWNrOicsIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5zdGFjayA6ICdObyBzdGFjayB0cmFjZScpXG4gICAgICAgIHJldHVybiBbXVxuICAgIH1cbn1cblxuLyoqXG4gKiBGaWx0ZXIgdW5pZmllZCB0cmFpbmluZyBkYXRhIGJ5IGNhdGVnb3J5XG4gKiBAcGFyYW0gZGF0YSAtIFVuaWZpZWQgdHJhaW5pbmcgZGF0YSBhcnJheVxuICogQHBhcmFtIGNhdGVnb3JpZXMgLSBDYXRlZ29yaWVzIHRvIGluY2x1ZGVcbiAqIEByZXR1cm5zIEZpbHRlcmVkIGRhdGEgYXJyYXlcbiAqL1xuZXhwb3J0IGNvbnN0IGZpbHRlclRyYWluaW5nRGF0YUJ5Q2F0ZWdvcnkgPSAoXG4gICAgZGF0YTogVW5pZmllZFRyYWluaW5nRGF0YVtdLFxuICAgIGNhdGVnb3JpZXM6IEFycmF5PCdvdmVyZHVlJyB8ICd1cGNvbWluZycgfCAnY29tcGxldGVkJz5cbik6IFVuaWZpZWRUcmFpbmluZ0RhdGFbXSA9PiB7XG4gICAgcmV0dXJuIGRhdGEuZmlsdGVyKGl0ZW0gPT4gY2F0ZWdvcmllcy5pbmNsdWRlcyhpdGVtLmNhdGVnb3J5KSlcbn1cblxuLyoqXG4gKiBHZXQgdHJhaW5pbmcgZGF0YSBzdGF0aXN0aWNzXG4gKiBAcGFyYW0gZGF0YSAtIFVuaWZpZWQgdHJhaW5pbmcgZGF0YSBhcnJheVxuICogQHJldHVybnMgU3RhdGlzdGljcyBvYmplY3Qgd2l0aCBjb3VudHMgYnkgY2F0ZWdvcnlcbiAqL1xuZXhwb3J0IGNvbnN0IGdldFRyYWluaW5nRGF0YVN0YXRzID0gKGRhdGE6IFVuaWZpZWRUcmFpbmluZ0RhdGFbXSkgPT4ge1xuICAgIHJldHVybiB7XG4gICAgICAgIHRvdGFsOiBkYXRhLmxlbmd0aCxcbiAgICAgICAgb3ZlcmR1ZTogZGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLmNhdGVnb3J5ID09PSAnb3ZlcmR1ZScpLmxlbmd0aCxcbiAgICAgICAgdXBjb21pbmc6IGRhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5jYXRlZ29yeSA9PT0gJ3VwY29taW5nJykubGVuZ3RoLFxuICAgICAgICBjb21wbGV0ZWQ6IGRhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5jYXRlZ29yeSA9PT0gJ2NvbXBsZXRlZCcpLmxlbmd0aFxuICAgIH1cbn1cblxuLyoqXG4gKiBEZWJ1ZyBmdW5jdGlvbiB0byBhbmFseXplIHRyYWluaW5nIGRhdGEgZm9yIGR1cGxpY2F0ZXMgYW5kIGlzc3Vlc1xuICogT25seSB1c2UgZm9yIGRlYnVnZ2luZyAtIG5vdCBmb3IgcHJvZHVjdGlvblxuICogQHBhcmFtIGRhdGEgLSBVbmlmaWVkIHRyYWluaW5nIGRhdGEgYXJyYXlcbiAqIEBwYXJhbSBsYWJlbCAtIExhYmVsIGZvciB0aGUgZGVidWcgb3V0cHV0XG4gKi9cbmV4cG9ydCBjb25zdCBkZWJ1Z1RyYWluaW5nRGF0YSA9IChkYXRhOiBVbmlmaWVkVHJhaW5pbmdEYXRhW10sIGxhYmVsOiBzdHJpbmcgPSAnVHJhaW5pbmcgRGF0YScpID0+IHtcbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdkZXZlbG9wbWVudCcpIHJldHVyblxuXG4gICAgY29uc3QgaWRzID0gZGF0YS5tYXAoaXRlbSA9PiBpdGVtLmlkKVxuICAgIGNvbnN0IGR1cGxpY2F0ZUlkcyA9IGlkcy5maWx0ZXIoKGlkLCBpbmRleCkgPT4gaWRzLmluZGV4T2YoaWQpICE9PSBpbmRleClcblxuICAgIGNvbnNvbGUuZ3JvdXAoYPCflI0gJHtsYWJlbH0gQW5hbHlzaXNgKVxuICAgIGNvbnNvbGUubG9nKCdUb3RhbCByZWNvcmRzOicsIGRhdGEubGVuZ3RoKVxuICAgIGNvbnNvbGUubG9nKCdDYXRlZ29yaWVzOicsIGRhdGEucmVkdWNlKChhY2MsIGl0ZW0pID0+IHtcbiAgICAgICAgYWNjW2l0ZW0uY2F0ZWdvcnldID0gKGFjY1tpdGVtLmNhdGVnb3J5XSB8fCAwKSArIDFcbiAgICAgICAgcmV0dXJuIGFjY1xuICAgIH0sIHt9IGFzIFJlY29yZDxzdHJpbmcsIG51bWJlcj4pKVxuXG4gICAgaWYgKGR1cGxpY2F0ZUlkcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIER1cGxpY2F0ZSBJRHMgZm91bmQ6JywgQXJyYXkuZnJvbShuZXcgU2V0KGR1cGxpY2F0ZUlkcykpKVxuICAgICAgICBjb25zb2xlLmxvZygnRHVwbGljYXRlIHJlY29yZHM6JywgZGF0YS5maWx0ZXIoaXRlbSA9PiBkdXBsaWNhdGVJZHMuaW5jbHVkZXMoaXRlbS5pZCkpKVxuICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgTm8gZHVwbGljYXRlcyBmb3VuZCcpXG4gICAgfVxuXG4gICAgY29uc29sZS5sb2coJ1NhbXBsZSByZWNvcmRzIGJ5IGNhdGVnb3J5OicpXG4gICAgY29uc3QgY2F0ZWdvcmllcyA9IFsnb3ZlcmR1ZScsICd1cGNvbWluZycsICdjb21wbGV0ZWQnXSBhcyBjb25zdFxuICAgIGNhdGVnb3JpZXMuZm9yRWFjaChjYXRlZ29yeSA9PiB7XG4gICAgICAgIGNvbnN0IHNhbXBsZSA9IGRhdGEuZmluZChpdGVtID0+IGl0ZW0uY2F0ZWdvcnkgPT09IGNhdGVnb3J5KVxuICAgICAgICBpZiAoc2FtcGxlKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhgJHtjYXRlZ29yeX06YCwge1xuICAgICAgICAgICAgICAgIGlkOiBzYW1wbGUuaWQsXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiBzYW1wbGUudHJhaW5pbmdUeXBlPy50aXRsZSxcbiAgICAgICAgICAgICAgICBkdWVEYXRlOiBzYW1wbGUuZHVlRGF0ZSxcbiAgICAgICAgICAgICAgICB2ZXNzZWw6IHNhbXBsZS52ZXNzZWw/LnRpdGxlLFxuICAgICAgICAgICAgICAgIG1lbWJlcnNDb3VudDogc2FtcGxlLm1lbWJlcnM/Lmxlbmd0aCB8fCAwXG4gICAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgfSlcbiAgICBjb25zb2xlLmdyb3VwRW5kKClcbn1cbiJdLCJuYW1lcyI6WyJHZXRUcmFpbmluZ1Nlc3Npb25TdGF0dXMiLCJUcmFpbmluZ1ByaW9yaXR5IiwidHJhbnNmb3JtQ29tcGxldGVkVHJhaW5pbmdUb1VuaWZpZWRGb3JtYXQiLCJ0cmFpbmluZ0xpc3QiLCJnZXRWZXNzZWxXaXRoSWNvbiIsIkFycmF5IiwiaXNBcnJheSIsIm1hcCIsInRyYWluaW5nIiwiY29tcGxldGVWZXNzZWxEYXRhIiwidmVzc2VsIiwiaWQiLCJ0aXRsZSIsInBvc2l0aW9uIiwidHJhaW5pbmdMb2NhdGlvblR5cGUiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwicmF3TWVtYmVycyIsIm1lbWJlcnMiLCJub2RlcyIsImRlZHVwbGljYXRlZE1lbWJlcnMiLCJyZWR1Y2UiLCJhY2MiLCJtZW1iZXIiLCJleGlzdGluZ01lbWJlciIsImZpbmQiLCJtIiwiZmlyc3ROYW1lIiwic3VybmFtZSIsImVtYWlsIiwicHVzaCIsImR1ZURhdGUiLCJkYXRlIiwidmVzc2VsSUQiLCJ0cmFpbmluZ1R5cGVJRCIsInRyYWluaW5nVHlwZXMiLCJ0cmFpbmluZ1R5cGUiLCJzdGF0dXMiLCJsYWJlbCIsImlzT3ZlcmR1ZSIsImNsYXNzIiwiZHVlV2l0aGluU2V2ZW5EYXlzIiwiY2F0ZWdvcnkiLCJvcmlnaW5hbERhdGEiLCJ0cmFuc2Zvcm1UcmFpbmluZ1Nlc3Npb25EdWVzVG9VbmlmaWVkRm9ybWF0IiwidHJhaW5pbmdTZXNzaW9uRHVlcyIsImZpbHRlcmVkRGF0YSIsImZpbHRlciIsIml0ZW0iLCJtZW1iZXJJRCIsImhhc1ZhbGlkVmVzc2VsTWVtYmVycyIsInNlYUxvZ3NNZW1iZXJzIiwic29tZSIsImxvZyIsIm9yaWdpbmFsQ291bnQiLCJsZW5ndGgiLCJmaWx0ZXJlZENvdW50IiwicmVtb3ZlZENvdW50IiwiZHVlV2l0aFN0YXR1cyIsImR1ZSIsInRvdGFsUmVjb3JkcyIsInN0YXR1c0JyZWFrZG93biIsImtleSIsImdyb3VwZWREdWVzIiwidHJhaW5pbmdTZXNzaW9uIiwiZ3JvdXBDb3VudCIsIk9iamVjdCIsImtleXMiLCJncm91cEtleXMiLCJzYW1wbGVHcm91cCIsInZhbHVlcyIsIm1lcmdlZER1ZXMiLCJncm91cCIsIm1lbWJlcnNDb3VudCIsIm1lbWJlck1hcCIsIk1hcCIsImZvckVhY2giLCJnZXQiLCJzZXQiLCJtZXJnZWRNZW1iZXJzIiwiZnJvbSIsImVuaGFuY2VkVmVzc2VsIiwicmVzdWx0IiwiQm9vbGVhbiIsImNhdGVnb3J5QnJlYWtkb3duIiwic2FtcGxlUmVjb3JkIiwiYWxsUmVjb3JkcyIsImdldFRyYWluaW5nUHJpb3JpdHkiLCJkZWR1cGxpY2F0ZVRyYWluaW5nRGF0YSIsImRhdGEiLCJyZWNvcmRNYXAiLCJpdGVtSWQiLCJwYXJzZUludCIsImlzTmFOIiwiaGFzIiwiZXhpc3RpbmciLCJkZWR1cGxpY2F0ZWREYXRhIiwiZGVkdXBsaWNhdGVkQ291bnQiLCJyZW1vdmVkRHVwbGljYXRlcyIsInNvcnRVbmlmaWVkVHJhaW5pbmdEYXRhIiwiY2F0ZWdvcmllcyIsInNvcnQiLCJhIiwiYiIsInByaW9yaXR5RGlmZiIsImRhdGVBIiwiRGF0ZSIsImdldFRpbWUiLCJkYXRlQiIsIm1lcmdlQW5kU29ydENyZXdUcmFpbmluZ0RhdGEiLCJjb21wbGV0ZWRUcmFpbmluZ0xpc3QiLCJpbmNsdWRlQ29tcGxldGVkIiwiZGVidWciLCJ0cmFpbmluZ1Nlc3Npb25EdWVzQ291bnQiLCJjb21wbGV0ZWRUcmFpbmluZ0xpc3RDb3VudCIsInRyYW5zZm9ybWVkRHVlcyIsImNvdW50Iiwic2FtcGxlIiwidHJhbnNmb3JtZWRDb21wbGV0ZWQiLCJjb21iaW5lZERhdGEiLCJ0b3RhbENvdW50IiwiZHVlc0NvdW50IiwiY29tcGxldGVkQ291bnQiLCJzb3J0ZWREYXRhIiwiZGVidWdUcmFpbmluZ0RhdGEiLCJFcnJvciIsInN0YWNrIiwiZmlsdGVyVHJhaW5pbmdEYXRhQnlDYXRlZ29yeSIsImluY2x1ZGVzIiwiZ2V0VHJhaW5pbmdEYXRhU3RhdHMiLCJ0b3RhbCIsIm92ZXJkdWUiLCJ1cGNvbWluZyIsImNvbXBsZXRlZCIsInByb2Nlc3MiLCJpZHMiLCJkdXBsaWNhdGVJZHMiLCJpbmRleCIsImluZGV4T2YiLCJTZXQiLCJncm91cEVuZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\n"));

/***/ })

});