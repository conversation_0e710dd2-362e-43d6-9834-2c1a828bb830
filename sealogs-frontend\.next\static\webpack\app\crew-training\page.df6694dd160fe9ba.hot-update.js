"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    // Debug initial filter state\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Hook initialized:\", {\n        initialFilter,\n        actualInitialFilter: filter,\n        hasMembers: !!filter.members,\n        membersFilter: filter.members\n    });\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            dataLength: Array.isArray(data) ? data.length : \"N/A\",\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing member filter:\", {\n                data,\n                dataType: typeof data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                currentMembersFilter: filter.members\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.members = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (array):\", {\n                    mappedValues,\n                    newFilter: next.members\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.members = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (single):\", {\n                    eqValue,\n                    newFilter: next.members\n                });\n            } else {\n                delete next.members;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Cleared member filter\");\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter update complete:\", {\n            type,\n            previousFilter: filter,\n            newFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] filteredData calculation triggered:\", {\n            unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n            debouncedFilter,\n            hasUnifiedData: !!unifiedData,\n            isArray: Array.isArray(unifiedData)\n        });\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data available, returning empty array\");\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        // Temporarily disable cache to debug filter issues\n        // if (filterCache.has(cacheKey)) {\n        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')\n        //     return filterCache.get(cacheKey)!\n        // }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting fresh filter calculation:\", {\n            totalItems: unifiedData.length,\n            filter: debouncedFilter\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                if (item.originalData) {\n                    var _item_originalData_trainer;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                        if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter - only apply if explicitly set by user with actual selections\n            if (debouncedFilter.members && (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) || ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in) && debouncedFilter.members.id.in.length > 0)) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Applying member filter to item:\", {\n                    itemId: item.id,\n                    itemCategory: item.category,\n                    itemMembers: item.members,\n                    memberFilter: debouncedFilter.members\n                });\n                if (item.members && item.members.length > 0) {\n                    var _debouncedFilter_members_id2, _debouncedFilter_members_id3;\n                    const memberIds = item.members.map((member)=>member.id);\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item member IDs:\", memberIds);\n                    // Single member filter (eq)\n                    if ((_debouncedFilter_members_id2 = debouncedFilter.members.id) === null || _debouncedFilter_members_id2 === void 0 ? void 0 : _debouncedFilter_members_id2.eq) {\n                        if (!memberIds.includes(debouncedFilter.members.id.eq)) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (eq):\", {\n                                itemId: item.id,\n                                expectedMemberId: debouncedFilter.members.id.eq,\n                                actualMemberIds: memberIds\n                            });\n                            return false;\n                        }\n                    }\n                    // Multiple member filter (in) - Use OR logic: show if ANY selected member is in the training\n                    if (((_debouncedFilter_members_id3 = debouncedFilter.members.id) === null || _debouncedFilter_members_id3 === void 0 ? void 0 : _debouncedFilter_members_id3.in) && debouncedFilter.members.id.in.length > 0) {\n                        const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                        if (!hasMatchingMember) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (in - OR logic):\", {\n                                itemId: item.id,\n                                expectedMemberIds: debouncedFilter.members.id.in,\n                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map((id)=>\"ID:\".concat(id)),\n                                actualMemberIds: memberIds,\n                                actualMemberIdsDetailed: memberIds.map((id)=>\"ID:\".concat(id)),\n                                intersection: memberIds.filter((id)=>{\n                                    var _debouncedFilter_members_id_in, _debouncedFilter_members_id, _debouncedFilter_members;\n                                    return (_debouncedFilter_members = debouncedFilter.members) === null || _debouncedFilter_members === void 0 ? void 0 : (_debouncedFilter_members_id = _debouncedFilter_members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : (_debouncedFilter_members_id_in = _debouncedFilter_members_id.in) === null || _debouncedFilter_members_id_in === void 0 ? void 0 : _debouncedFilter_members_id_in.includes(id);\n                                }),\n                                logic: \"OR - needs at least one matching member\"\n                            });\n                            return false;\n                        } else {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item passed member filter (in - OR logic):\", {\n                                itemId: item.id,\n                                matchingMemberIds: debouncedFilter.members.id.in.filter((id)=>memberIds.includes(id)),\n                                logic: \"OR - found at least one matching member\"\n                            });\n                        }\n                    }\n                } else {\n                    // If item has no members but filter is set, filter it out\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item has no members, filtering out:\", {\n                        itemId: item.id,\n                        itemCategory: item.category,\n                        memberFilter: debouncedFilter.members\n                    });\n                    return false;\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter calculation complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key]),\n            sampleFilteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_members;\n                return {\n                    id: item.id,\n                    category: item.category,\n                    memberCount: ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.length) || 0\n                };\n            })\n        });\n        // Performance optimization: Cache the result (temporarily disabled for debugging)\n        // if (filterCache.size >= CACHE_SIZE_LIMIT) {\n        //     // Remove oldest entries when cache is full\n        //     const firstKey = filterCache.keys().next().value\n        //     if (firstKey) {\n        //         filterCache.delete(firstKey)\n        //     }\n        // }\n        // filterCache.set(cacheKey, filtered)\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});