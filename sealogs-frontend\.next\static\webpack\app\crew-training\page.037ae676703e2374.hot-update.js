"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    var _stack;\n    const { initialFilter, unifiedData } = opts;\n    // Add debugging to track where the filter state is coming from\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Hook called with opts:\", {\n        initialFilter,\n        initialFilterStringified: JSON.stringify(initialFilter),\n        unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n        stackTrace: (_stack = new Error().stack) === null || _stack === void 0 ? void 0 : _stack.split(\"\\n\").slice(1, 5).join(\"\\n\")\n    });\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] useState initializer called:\", {\n            initialFilter,\n            initialFilterStringified: JSON.stringify(initialFilter),\n            willUseEmptyObject: true\n        });\n        return {};\n    });\n    // Debug initial filter state with detailed information\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Hook initialized with detailed state:\", {\n        initialFilter,\n        initialFilterStringified: JSON.stringify(initialFilter),\n        actualInitialFilter: filter,\n        actualFilterStringified: JSON.stringify(filter),\n        hasMembers: !!filter.members,\n        membersFilter: filter.members,\n        membersFilterStringified: JSON.stringify(filter.members),\n        initialFilterHasMembers: !!initialFilter.members,\n        initialFilterMembersFilter: initialFilter.members\n    });\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] debouncedFilter useState initializer called:\", {\n            initialFilter,\n            initialFilterStringified: JSON.stringify(initialFilter),\n            willUseInitialFilter: true\n        });\n        return initialFilter;\n    });\n    // Debug debounced filter initialization\n    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounced filter initialized:\", {\n        debouncedFilter,\n        debouncedFilterStringified: JSON.stringify(debouncedFilter),\n        hasDebouncedMembers: !!debouncedFilter.members,\n        debouncedMembersFilter: debouncedFilter.members\n    });\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter change detected, setting up debounce:\", {\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            hasMembers: !!filter.members,\n            membersFilter: filter.members\n        });\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Debounce timeout triggered, updating debouncedFilter:\", {\n                newDebouncedFilter: filter,\n                newDebouncedFilterStringified: JSON.stringify(filter),\n                previousDebouncedFilter: debouncedFilter,\n                previousDebouncedFilterStringified: JSON.stringify(debouncedFilter)\n            });\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter,\n        debouncedFilter\n    ]);\n    // Add useEffect to track filter state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _stack;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter state changed:\", {\n            filter,\n            filterStringified: JSON.stringify(filter),\n            hasMembers: !!filter.members,\n            membersFilter: filter.members,\n            membersFilterStringified: JSON.stringify(filter.members),\n            stackTrace: (_stack = new Error().stack) === null || _stack === void 0 ? void 0 : _stack.split(\"\\n\").slice(1, 8).join(\"\\n\")\n        });\n    }, [\n        filter\n    ]);\n    // Add useEffect to track debouncedFilter state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] DebouncedFilter state changed:\", {\n            debouncedFilter,\n            debouncedFilterStringified: JSON.stringify(debouncedFilter),\n            hasDebouncedMembers: !!debouncedFilter.members,\n            debouncedMembersFilter: debouncedFilter.members,\n            debouncedMembersFilterStringified: JSON.stringify(debouncedFilter.members)\n        });\n    }, [\n        debouncedFilter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            dataLength: Array.isArray(data) ? data.length : \"N/A\",\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            isCrewFilter: type === \"member\"\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n            } else {\n                delete next.trainingTypes;\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n            } else {\n                delete next.trainer;\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing member filter:\", {\n                data,\n                dataType: typeof data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                currentMembersFilter: filter.members\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.members = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (array):\", {\n                    mappedValues,\n                    newFilter: next.members\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.members = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set member filter (single):\", {\n                    eqValue,\n                    newFilter: next.members\n                });\n            } else {\n                delete next.members;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Cleared member filter\");\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter update complete:\", {\n            type,\n            previousFilter: filter,\n            newFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] filteredData calculation triggered:\", {\n            unifiedDataLength: (unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length) || 0,\n            debouncedFilter,\n            hasUnifiedData: !!unifiedData,\n            isArray: Array.isArray(unifiedData)\n        });\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] No unified data available, returning empty array\");\n            return [];\n        }\n        // Performance optimization: Check cache first (temporarily disabled for debugging)\n        // const dataHash = generateDataHash(unifiedData)\n        // const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash)\n        // if (filterCache.has(cacheKey)) {\n        //     console.log('🔍 [useUnifiedTrainingFilters] Using cached filter result')\n        //     return filterCache.get(cacheKey)!\n        // }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting fresh filter calculation:\", {\n            totalItems: unifiedData.length,\n            filter: debouncedFilter\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                if (((_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                if (item.originalData) {\n                    var _item_originalData_trainer;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                        if (((_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter - only apply if explicitly set by user\n            if (debouncedFilter.members && (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) || ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in))) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Applying member filter to item:\", {\n                    itemId: item.id,\n                    itemCategory: item.category,\n                    itemMembers: item.members,\n                    memberFilter: debouncedFilter.members\n                });\n                if (item.members && item.members.length > 0) {\n                    var _debouncedFilter_members_id2, _debouncedFilter_members_id3;\n                    // Convert member IDs to numbers for consistent comparison\n                    const memberIds = item.members.map((member)=>{\n                        const id = member.id;\n                        return typeof id === \"string\" ? parseInt(id, 10) : id;\n                    });\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item member IDs (converted to numbers):\", memberIds);\n                    if (((_debouncedFilter_members_id2 = debouncedFilter.members.id) === null || _debouncedFilter_members_id2 === void 0 ? void 0 : _debouncedFilter_members_id2.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (eq):\", {\n                            itemId: item.id,\n                            expectedMemberId: debouncedFilter.members.id.eq,\n                            actualMemberIds: memberIds\n                        });\n                        return false;\n                    }\n                    if (((_debouncedFilter_members_id3 = debouncedFilter.members.id) === null || _debouncedFilter_members_id3 === void 0 ? void 0 : _debouncedFilter_members_id3.in) && debouncedFilter.members.id.in.length > 0) {\n                        const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                        if (!hasMatchingMember) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out (in):\", {\n                                itemId: item.id,\n                                expectedMemberIds: debouncedFilter.members.id.in,\n                                expectedMemberIdsDetailed: debouncedFilter.members.id.in.map((id)=>\"ID:\".concat(id)),\n                                actualMemberIds: memberIds,\n                                actualMemberIdsDetailed: memberIds.map((id)=>\"ID:\".concat(id)),\n                                intersection: memberIds.filter((id)=>{\n                                    var _debouncedFilter_members_id_in, _debouncedFilter_members_id, _debouncedFilter_members;\n                                    return (_debouncedFilter_members = debouncedFilter.members) === null || _debouncedFilter_members === void 0 ? void 0 : (_debouncedFilter_members_id = _debouncedFilter_members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : (_debouncedFilter_members_id_in = _debouncedFilter_members_id.in) === null || _debouncedFilter_members_id_in === void 0 ? void 0 : _debouncedFilter_members_id_in.includes(id);\n                                }),\n                                dataTypeComparison: {\n                                    expectedTypes: debouncedFilter.members.id.in.map((id)=>typeof id),\n                                    actualTypes: memberIds.map((id)=>typeof id)\n                                }\n                            });\n                            return false;\n                        } else {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item passed member filter (in):\", {\n                                itemId: item.id,\n                                matchingMemberIds: debouncedFilter.members.id.in.filter((id)=>memberIds.includes(id))\n                            });\n                        }\n                    }\n                } else {\n                    // If item has no members but filter is set, filter it out\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item has no members, filtering out:\", {\n                        itemId: item.id,\n                        itemCategory: item.category,\n                        memberFilter: debouncedFilter.members\n                    });\n                    return false;\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter calculation complete:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: Object.keys(debouncedFilter).filter((key)=>debouncedFilter[key]),\n            sampleFilteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_members;\n                return {\n                    id: item.id,\n                    category: item.category,\n                    memberCount: ((_item_members = item.members) === null || _item_members === void 0 ? void 0 : _item_members.length) || 0\n                };\n            })\n        });\n        // Performance optimization: Cache the result (temporarily disabled for debugging)\n        // if (filterCache.size >= CACHE_SIZE_LIMIT) {\n        //     // Remove oldest entries when cache is full\n        //     const firstKey = filterCache.keys().next().value\n        //     if (firstKey) {\n        //         filterCache.delete(firstKey)\n        //     }\n        // }\n        // filterCache.set(cacheKey, filtered)\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    // Wrap setFilter to add debugging\n    const debugSetFilter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newFilter)=>{\n        var _stack;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] setFilter called:\", {\n            newFilter,\n            newFilterStringified: JSON.stringify(newFilter),\n            currentFilter: filter,\n            currentFilterStringified: JSON.stringify(filter),\n            stackTrace: (_stack = new Error().stack) === null || _stack === void 0 ? void 0 : _stack.split(\"\\n\").slice(1, 8).join(\"\\n\")\n        });\n        setFilter(newFilter);\n    }, [\n        filter\n    ]);\n    return {\n        filter,\n        setFilter: debugSetFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});