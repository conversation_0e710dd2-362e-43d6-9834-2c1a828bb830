"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts":
/*!*********************************************************************!*\
  !*** ./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnifiedTrainingFilters: function() { return /* binding */ useUnifiedTrainingFilters; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n// Performance optimization: Cache filter results to avoid redundant calculations\nconst filterCache = new Map();\nconst CACHE_SIZE_LIMIT = 50 // Limit cache size to prevent memory issues\n;\n// Helper function to generate cache key from filter and data\nconst generateCacheKey = (filter, dataLength, dataHash)=>{\n    return JSON.stringify({\n        filter,\n        dataLength,\n        dataHash\n    });\n};\n// Helper function to generate a simple hash from data array\nconst generateDataHash = (data)=>{\n    var _data_, _data_1;\n    if (!data || data.length === 0) return \"empty\";\n    // Use first and last item IDs plus length for a simple hash\n    return \"\".concat((_data_ = data[0]) === null || _data_ === void 0 ? void 0 : _data_.id, \"-\").concat((_data_1 = data[data.length - 1]) === null || _data_1 === void 0 ? void 0 : _data_1.id, \"-\").concat(data.length);\n};\n/**\n * Hook for filtering unified training data on the client side\n * Works with merged training data from mergeAndSortCrewTrainingData\n */ function useUnifiedTrainingFilters(opts) {\n    const { initialFilter, unifiedData } = opts;\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const [debouncedFilter, setDebouncedFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialFilter);\n    const debounceTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // Debounce filter changes to improve performance during rapid changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (debounceTimeoutRef.current) {\n            clearTimeout(debounceTimeoutRef.current);\n        }\n        debounceTimeoutRef.current = setTimeout(()=>{\n            setDebouncedFilter(filter);\n        }, 300) // 300ms debounce delay\n        ;\n        return ()=>{\n            if (debounceTimeoutRef.current) {\n                clearTimeout(debounceTimeoutRef.current);\n            }\n        };\n    }, [\n        filter\n    ]);\n    const handleFilterChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((param)=>{\n        let { type, data } = param;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] handleFilterChange called:\", {\n            type,\n            data,\n            dataType: typeof data,\n            isArray: Array.isArray(data),\n            currentFilter: filter\n        });\n        const next = {\n            ...filter\n        };\n        /* ---- vessel ------------------------------------------------------- */ if (type === \"vessel\") {\n            if (Array.isArray(data) && data.length) {\n                next.vesselID = {\n                    in: data.map((d)=>+d.value)\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.vesselID = {\n                    eq: +data.value\n                };\n            } else {\n                delete next.vesselID;\n            }\n        }\n        /* ---- trainingType ------------------------------------------------- */ if (type === \"trainingType\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing trainingType filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataValue: data === null || data === void 0 ? void 0 : data.value,\n                currentTrainingTypes: filter.trainingTypes\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainingTypes = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (array):\", {\n                    mappedValues,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else if (data && !Array.isArray(data)) {\n                const containsValue = +data.value;\n                next.trainingTypes = {\n                    id: {\n                        contains: containsValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainingTypes (single):\", {\n                    containsValue,\n                    nextTrainingTypes: next.trainingTypes\n                });\n            } else {\n                delete next.trainingTypes;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Deleted trainingTypes filter\");\n            }\n        }\n        /* ---- trainer ------------------------------------------------------ */ if (type === \"trainer\") {\n            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Processing trainer filter:\", {\n                data,\n                isArray: Array.isArray(data),\n                dataLength: Array.isArray(data) ? data.length : \"N/A\",\n                dataValue: data === null || data === void 0 ? void 0 : data.value,\n                currentTrainer: filter.trainer\n            });\n            if (Array.isArray(data) && data.length) {\n                const mappedValues = data.map((d)=>+d.value);\n                next.trainer = {\n                    id: {\n                        in: mappedValues\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainer (array):\", {\n                    mappedValues,\n                    nextTrainer: next.trainer\n                });\n            } else if (data && !Array.isArray(data)) {\n                const eqValue = +data.value;\n                next.trainer = {\n                    id: {\n                        eq: eqValue\n                    }\n                };\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Set trainer (single):\", {\n                    eqValue,\n                    nextTrainer: next.trainer\n                });\n            } else {\n                delete next.trainer;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Deleted trainer filter\");\n            }\n        }\n        /* ---- member ------------------------------------------------------- */ if (type === \"member\") {\n            if (Array.isArray(data) && data.length) {\n                next.members = {\n                    id: {\n                        in: data.map((d)=>+d.value)\n                    }\n                };\n            } else if (data && !Array.isArray(data)) {\n                next.members = {\n                    id: {\n                        eq: +data.value\n                    }\n                };\n            } else {\n                delete next.members;\n            }\n        }\n        /* ---- dateRange ---------------------------------------------------- */ if (type === \"dateRange\") {\n            if ((data === null || data === void 0 ? void 0 : data.startDate) && (data === null || data === void 0 ? void 0 : data.endDate)) {\n                next.date = {\n                    gte: data.startDate,\n                    lte: data.endDate\n                };\n            } else {\n                delete next.date;\n            }\n        }\n        /* ---- category ----------------------------------------------------- */ if (type === \"category\") {\n            if (data && data !== \"all\") {\n                next.category = data;\n            } else {\n                delete next.category;\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Final filter state:\", {\n            type,\n            previousFilter: filter,\n            nextFilter: next,\n            filterChanged: JSON.stringify(filter) !== JSON.stringify(next)\n        });\n        setFilter(next);\n    }, [\n        filter\n    ]);\n    // Performance-optimized client-side filtering of unified data\n    const filteredData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (!unifiedData || !Array.isArray(unifiedData)) {\n            return [];\n        }\n        // Performance optimization: Check cache first\n        const dataHash = generateDataHash(unifiedData);\n        const cacheKey = generateCacheKey(debouncedFilter, unifiedData.length, dataHash);\n        if (filterCache.has(cacheKey)) {\n            console.log(\"\\uD83D\\uDE80 [useUnifiedTrainingFilters] Using cached filter result\");\n            return filterCache.get(cacheKey);\n        }\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Starting filter operation:\", {\n            unifiedDataLength: unifiedData.length,\n            debouncedFilter,\n            hasTrainingTypeFilter: !!debouncedFilter.trainingTypes\n        });\n        const startTime = performance.now();\n        // Optimized filtering with early returns for better performance\n        const filtered = unifiedData.filter((item)=>{\n            var _item_trainingType, _item_trainingType1;\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filtering item:\", {\n                itemId: item.id,\n                category: item.category,\n                trainingType: (_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title,\n                vesselID: item.vesselID,\n                hasOriginalData: !!item.originalData,\n                originalDataKeys: item.originalData ? Object.keys(item.originalData) : [],\n                originalDataSample: item.originalData ? {\n                    trainerID: item.originalData.trainerID,\n                    trainer: item.originalData.trainer,\n                    hasTrainer: \"trainer\" in item.originalData,\n                    hasTrainerID: \"trainerID\" in item.originalData\n                } : null,\n                debouncedFilter\n            });\n            // Category filter\n            if (debouncedFilter.category && debouncedFilter.category !== \"all\") {\n                if (item.category !== debouncedFilter.category) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by category:\", {\n                        itemCategory: item.category,\n                        filterCategory: debouncedFilter.category\n                    });\n                    return false;\n                }\n            }\n            // Vessel filter\n            if (debouncedFilter.vesselID) {\n                // Convert vessel ID to number for comparison since data might be strings but filter values are numbers\n                const itemVesselIdNum = typeof item.vesselID === \"string\" ? parseInt(item.vesselID, 10) : item.vesselID;\n                console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Vessel filter check:\", {\n                    itemVesselID: item.vesselID,\n                    itemVesselIdNum,\n                    filterVesselID: debouncedFilter.vesselID,\n                    itemVesselIDType: typeof item.vesselID\n                });\n                if (debouncedFilter.vesselID.eq && itemVesselIdNum !== debouncedFilter.vesselID.eq) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by vessel eq:\", {\n                        itemVesselID: item.vesselID,\n                        itemVesselIdNum,\n                        expectedVesselID: debouncedFilter.vesselID.eq\n                    });\n                    return false;\n                }\n                if (debouncedFilter.vesselID.in && !debouncedFilter.vesselID.in.includes(itemVesselIdNum)) {\n                    console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item filtered out by vessel in:\", {\n                        itemVesselID: item.vesselID,\n                        itemVesselIdNum,\n                        expectedVesselIDs: debouncedFilter.vesselID.in\n                    });\n                    return false;\n                }\n            }\n            // Training type filter\n            if (debouncedFilter.trainingTypes) {\n                var _item_trainingType2, _item_trainingType3, _debouncedFilter_trainingTypes_id, _debouncedFilter_trainingTypes_id1, _debouncedFilter_trainingTypes_id2, _debouncedFilter_trainingTypes_id3;\n                const trainingTypeId = item.trainingTypeID || ((_item_trainingType2 = item.trainingType) === null || _item_trainingType2 === void 0 ? void 0 : _item_trainingType2.id);\n                // Convert to number for comparison since filter values are numbers but data might be strings\n                const trainingTypeIdNum = typeof trainingTypeId === \"string\" ? parseInt(trainingTypeId, 10) : trainingTypeId;\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Training type filter check:\", {\n                    itemId: item.id,\n                    trainingTypeTitle: (_item_trainingType3 = item.trainingType) === null || _item_trainingType3 === void 0 ? void 0 : _item_trainingType3.title,\n                    trainingTypeId,\n                    trainingTypeIdNum,\n                    filterContains: (_debouncedFilter_trainingTypes_id = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id === void 0 ? void 0 : _debouncedFilter_trainingTypes_id.contains,\n                    filterIn: (_debouncedFilter_trainingTypes_id1 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id1 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id1.in,\n                    item: {\n                        trainingTypeID: item.trainingTypeID,\n                        trainingType: item.trainingType,\n                        category: item.category\n                    }\n                });\n                if (((_debouncedFilter_trainingTypes_id2 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id2 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id2.contains) && trainingTypeIdNum !== debouncedFilter.trainingTypes.id.contains) {\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by contains check:\", {\n                        itemId: item.id,\n                        trainingTypeIdNum,\n                        expectedContains: debouncedFilter.trainingTypes.id.contains\n                    });\n                    return false;\n                }\n                if (((_debouncedFilter_trainingTypes_id3 = debouncedFilter.trainingTypes.id) === null || _debouncedFilter_trainingTypes_id3 === void 0 ? void 0 : _debouncedFilter_trainingTypes_id3.in) && !debouncedFilter.trainingTypes.id.in.includes(trainingTypeIdNum)) {\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by in check:\", {\n                        itemId: item.id,\n                        trainingTypeIdNum,\n                        expectedIn: debouncedFilter.trainingTypes.id.in\n                    });\n                    return false;\n                }\n            }\n            // Trainer filter (for completed training sessions)\n            if (debouncedFilter.trainer) {\n                console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Trainer filter check:\", {\n                    itemId: item.id,\n                    category: item.category,\n                    hasOriginalData: !!item.originalData,\n                    originalData: item.originalData,\n                    filterTrainer: debouncedFilter.trainer,\n                    itemType: item.category,\n                    isOverdueOrUpcoming: item.category === \"overdue\" || item.category === \"upcoming\"\n                });\n                if (item.originalData) {\n                    var _item_originalData_trainer, _debouncedFilter_trainer_id, _debouncedFilter_trainer_id1;\n                    const trainerId = item.originalData.trainerID || ((_item_originalData_trainer = item.originalData.trainer) === null || _item_originalData_trainer === void 0 ? void 0 : _item_originalData_trainer.id);\n                    // Convert to number for comparison since filter values are numbers but data might be strings\n                    const trainerIdNum = typeof trainerId === \"string\" ? parseInt(trainerId, 10) : trainerId;\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Trainer ID check:\", {\n                        itemId: item.id,\n                        trainerId,\n                        trainerIdNum,\n                        trainerIdType: typeof trainerId,\n                        filterEq: (_debouncedFilter_trainer_id = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id === void 0 ? void 0 : _debouncedFilter_trainer_id.eq,\n                        filterIn: (_debouncedFilter_trainer_id1 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id1 === void 0 ? void 0 : _debouncedFilter_trainer_id1.in,\n                        originalDataKeys: Object.keys(item.originalData || {}),\n                        trainerData: item.originalData.trainer\n                    });\n                    if (trainerIdNum) {\n                        var _debouncedFilter_trainer_id2, _debouncedFilter_trainer_id3;\n                        if (((_debouncedFilter_trainer_id2 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id2 === void 0 ? void 0 : _debouncedFilter_trainer_id2.eq) && trainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer eq:\", {\n                                itemId: item.id,\n                                trainerIdNum,\n                                expectedTrainerID: debouncedFilter.trainer.id.eq\n                            });\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id3 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id3 === void 0 ? void 0 : _debouncedFilter_trainer_id3.in) && !debouncedFilter.trainer.id.in.includes(trainerIdNum)) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer in:\", {\n                                itemId: item.id,\n                                trainerIdNum,\n                                expectedTrainerIDs: debouncedFilter.trainer.id.in\n                            });\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // If trainer filter is applied but no trainer data exists, exclude this item\n                        console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out - no trainer data:\", {\n                            itemId: item.id,\n                            category: item.category,\n                            hasOriginalData: !!item.originalData,\n                            originalDataKeys: Object.keys(item.originalData || {})\n                        });\n                        return false;\n                    }\n                } else {\n                    var _itemAsAny_trainer;\n                    // For items without originalData (like overdue/upcoming), check if they have trainer info in other fields\n                    const itemAsAny = item// Type assertion to access potential trainer fields\n                    ;\n                    console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] No originalData, checking other trainer fields:\", {\n                        itemId: item.id,\n                        category: item.category,\n                        itemKeys: Object.keys(item),\n                        hasTrainerField: \"trainer\" in itemAsAny,\n                        hasTrainerID: \"trainerID\" in itemAsAny,\n                        trainer: itemAsAny.trainer,\n                        trainerID: itemAsAny.trainerID,\n                        isOverdueOrUpcoming: item.category === \"overdue\" || item.category === \"upcoming\"\n                    });\n                    // Check if item has trainer info in other fields\n                    const itemTrainerId = itemAsAny.trainerID || ((_itemAsAny_trainer = itemAsAny.trainer) === null || _itemAsAny_trainer === void 0 ? void 0 : _itemAsAny_trainer.id);\n                    const itemTrainerIdNum = typeof itemTrainerId === \"string\" ? parseInt(itemTrainerId, 10) : itemTrainerId;\n                    if (itemTrainerIdNum) {\n                        var _debouncedFilter_trainer_id4, _debouncedFilter_trainer_id5;\n                        if (((_debouncedFilter_trainer_id4 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id4 === void 0 ? void 0 : _debouncedFilter_trainer_id4.eq) && itemTrainerIdNum !== debouncedFilter.trainer.id.eq) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer eq (no originalData):\", {\n                                itemId: item.id,\n                                itemTrainerIdNum,\n                                expectedTrainerID: debouncedFilter.trainer.id.eq\n                            });\n                            return false;\n                        }\n                        if (((_debouncedFilter_trainer_id5 = debouncedFilter.trainer.id) === null || _debouncedFilter_trainer_id5 === void 0 ? void 0 : _debouncedFilter_trainer_id5.in) && !debouncedFilter.trainer.id.in.includes(itemTrainerIdNum)) {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out by trainer in (no originalData):\", {\n                                itemId: item.id,\n                                itemTrainerIdNum,\n                                expectedTrainerIDs: debouncedFilter.trainer.id.in\n                            });\n                            return false;\n                        }\n                    } else if (debouncedFilter.trainer.id) {\n                        // Special handling for overdue/upcoming training sessions:\n                        // Include them in results since they don't have trainers assigned yet\n                        if (item.category === \"overdue\" || item.category === \"upcoming\") {\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Including overdue/upcoming item (no trainer assigned yet):\", {\n                                itemId: item.id,\n                                category: item.category\n                            });\n                        // Don't filter out overdue/upcoming items - they should be included\n                        } else {\n                            // For other categories (like completed), if trainer filter is applied but no trainer data exists, exclude this item\n                            console.log(\"\\uD83C\\uDFAF [useUnifiedTrainingFilters] Item filtered out - no trainer data anywhere:\", {\n                                itemId: item.id,\n                                category: item.category\n                            });\n                            return false;\n                        }\n                    }\n                }\n            }\n            // Member filter\n            if (debouncedFilter.members && item.members) {\n                var _debouncedFilter_members_id, _debouncedFilter_members_id1;\n                const memberIds = item.members.map((member)=>member.id);\n                if (((_debouncedFilter_members_id = debouncedFilter.members.id) === null || _debouncedFilter_members_id === void 0 ? void 0 : _debouncedFilter_members_id.eq) && !memberIds.includes(debouncedFilter.members.id.eq)) {\n                    return false;\n                }\n                if ((_debouncedFilter_members_id1 = debouncedFilter.members.id) === null || _debouncedFilter_members_id1 === void 0 ? void 0 : _debouncedFilter_members_id1.in) {\n                    const hasMatchingMember = debouncedFilter.members.id.in.some((id)=>memberIds.includes(id));\n                    if (!hasMatchingMember) {\n                        return false;\n                    }\n                }\n            }\n            // Date filter\n            if (debouncedFilter.date && item.dueDate) {\n                const itemDate = new Date(item.dueDate);\n                if (debouncedFilter.date.gte && itemDate < debouncedFilter.date.gte) {\n                    return false;\n                }\n                if (debouncedFilter.date.lte && itemDate > debouncedFilter.date.lte) {\n                    return false;\n                }\n            }\n            console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Item passed all filters:\", {\n                itemId: item.id,\n                category: item.category,\n                trainingType: (_item_trainingType1 = item.trainingType) === null || _item_trainingType1 === void 0 ? void 0 : _item_trainingType1.title\n            });\n            return true;\n        });\n        const endTime = performance.now();\n        const filterTime = endTime - startTime;\n        console.log(\"\\uD83D\\uDD0D [useUnifiedTrainingFilters] Filter operation completed:\", {\n            originalCount: unifiedData.length,\n            filteredCount: filtered.length,\n            filterTime: \"\".concat(filterTime.toFixed(2), \"ms\"),\n            appliedFilters: debouncedFilter,\n            filteredItems: filtered.slice(0, 3).map((item)=>{\n                var _item_trainingType;\n                return {\n                    id: item.id,\n                    trainingType: (_item_trainingType = item.trainingType) === null || _item_trainingType === void 0 ? void 0 : _item_trainingType.title,\n                    category: item.category,\n                    trainingTypeID: item.trainingTypeID\n                };\n            })\n        });\n        // Performance optimization: Cache the result\n        if (filterCache.size >= CACHE_SIZE_LIMIT) {\n            // Remove oldest entries when cache is full\n            const firstKey = filterCache.keys().next().value;\n            if (firstKey) {\n                filterCache.delete(firstKey);\n            }\n        }\n        filterCache.set(cacheKey, filtered);\n        return filtered;\n    }, [\n        unifiedData,\n        debouncedFilter\n    ]);\n    return {\n        filter,\n        setFilter,\n        handleFilterChange,\n        filteredData\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/hooks/useUnifiedTrainingFilters.ts\n"));

/***/ })

});